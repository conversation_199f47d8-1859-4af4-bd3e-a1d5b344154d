import{d as z,u as M,r as j,c as A,f as e,w as t,j as o,e as u,h as a,q as D,t as I,s as P,n as s,l as y,v as g,x as R,y as U,z as $,A as F,B as G,b as H,C as J,p as K,k as O}from"./index-BDkHONMN.js";import{_ as Q}from"./_plugin-vue_export-helper-DlAUqK2U.js";const W={class:"layout-container"},X={class:"header-left"},Y={class:"header-right"},Z={class:"user-info"},h={class:"username"},ee={class:"main-content"},te=z({__name:"Layout",setup(ne){const r=H(),f=M(),_=j(!1),c=()=>{_.value=!_.value},C=()=>{r.push("/profile")},b=()=>{r.push("/system/config")},k=async()=>{try{await J.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await f.logout(),K.success("退出登录成功"),r.push("/login")}catch{}};return(p,n)=>{const d=o("el-icon"),B=o("el-avatar"),m=o("el-dropdown-item"),S=o("el-dropdown-menu"),E=o("el-dropdown"),L=o("el-header"),l=o("el-menu-item"),i=o("el-sub-menu"),N=o("el-menu"),T=o("el-aside"),V=o("router-view"),q=o("el-main"),x=o("el-container");return O(),A("div",W,[e(x,null,{default:t(()=>[e(L,{class:"layout-header"},{default:t(()=>[u("div",X,[e(d,{class:"menu-icon",onClick:c},{default:t(()=>[e(a(D))]),_:1}),n[0]||(n[0]=u("h1",{class:"system-title"},"食谱管理系统",-1))]),u("div",Y,[e(E,{class:"user-dropdown"},{dropdown:t(()=>[e(S,null,{default:t(()=>[e(m,{onClick:C},{default:t(()=>[e(d,null,{default:t(()=>[e(a(y))]),_:1}),n[1]||(n[1]=s(" 个人信息 "))]),_:1,__:[1]}),e(m,{onClick:b},{default:t(()=>[e(d,null,{default:t(()=>[e(a(g))]),_:1}),n[2]||(n[2]=s(" 系统设置 "))]),_:1,__:[2]}),e(m,{divided:"",onClick:k},{default:t(()=>[e(d,null,{default:t(()=>[e(a(R))]),_:1}),n[3]||(n[3]=s(" 退出登录 "))]),_:1,__:[3]})]),_:1})]),default:t(()=>{var v,w;return[u("span",Z,[e(B,{size:32,src:(v=a(f).user)==null?void 0:v.avatar},null,8,["src"]),u("span",h,I((w=a(f).user)==null?void 0:w.username),1),e(d,null,{default:t(()=>[e(a(P))]),_:1})])]}),_:1})])]),_:1}),e(x,null,{default:t(()=>[e(T,{width:_.value?"64px":"240px",class:"layout-sidebar"},{default:t(()=>[e(N,{"default-active":p.$route.path,collapse:_.value,"unique-opened":!0,router:"",class:"sidebar-menu"},{default:t(()=>[e(l,{index:"/dashboard"},{title:t(()=>n[4]||(n[4]=[s("控制台")])),default:t(()=>[e(d,null,{default:t(()=>[e(a(U))]),_:1})]),_:1}),e(i,{index:"user"},{title:t(()=>[e(d,null,{default:t(()=>[e(a(y))]),_:1}),n[5]||(n[5]=u("span",null,"用户管理",-1))]),default:t(()=>[e(l,{index:"/users"},{default:t(()=>n[6]||(n[6]=[s("用户列表")])),_:1,__:[6]}),e(l,{index:"/users/roles"},{default:t(()=>n[7]||(n[7]=[s("角色权限")])),_:1,__:[7]})]),_:1}),e(i,{index:"content"},{title:t(()=>[e(d,null,{default:t(()=>[e(a($))]),_:1}),n[8]||(n[8]=u("span",null,"内容管理",-1))]),default:t(()=>[e(l,{index:"/content/recipes"},{default:t(()=>n[9]||(n[9]=[s("食谱管理")])),_:1,__:[9]}),e(l,{index:"/content/audit"},{default:t(()=>n[10]||(n[10]=[s("内容审核")])),_:1,__:[10]}),e(l,{index:"/content/categories"},{default:t(()=>n[11]||(n[11]=[s("分类管理")])),_:1,__:[11]}),e(l,{index:"/content/sensitive-words"},{default:t(()=>n[12]||(n[12]=[s("敏感词管理")])),_:1,__:[12]})]),_:1}),e(i,{index:"stats"},{title:t(()=>[e(d,null,{default:t(()=>[e(a(F))]),_:1}),n[13]||(n[13]=u("span",null,"数据统计",-1))]),default:t(()=>[e(l,{index:"/stats/overview"},{default:t(()=>n[14]||(n[14]=[s("数据概览")])),_:1,__:[14]}),e(l,{index:"/stats/users"},{default:t(()=>n[15]||(n[15]=[s("用户统计")])),_:1,__:[15]}),e(l,{index:"/stats/content"},{default:t(()=>n[16]||(n[16]=[s("内容统计")])),_:1,__:[16]})]),_:1}),e(i,{index:"system"},{title:t(()=>[e(d,null,{default:t(()=>[e(a(g))]),_:1}),n[17]||(n[17]=u("span",null,"系统管理",-1))]),default:t(()=>[e(l,{index:"/system/config"},{default:t(()=>n[18]||(n[18]=[s("系统配置")])),_:1,__:[18]}),e(l,{index:"/system/email-templates"},{default:t(()=>n[19]||(n[19]=[s("邮件模板")])),_:1,__:[19]}),e(l,{index:"/system/announcements"},{default:t(()=>n[20]||(n[20]=[s("公告管理")])),_:1,__:[20]}),e(l,{index:"/system/logs"},{default:t(()=>n[21]||(n[21]=[s("日志管理")])),_:1,__:[21]})]),_:1}),e(l,{index:"/demo/advanced-features"},{title:t(()=>n[22]||(n[22]=[s("功能演示")])),default:t(()=>[e(d,null,{default:t(()=>[e(a(G))]),_:1})]),_:1})]),_:1},8,["default-active","collapse"])]),_:1},8,["width"]),e(q,{class:"layout-main"},{default:t(()=>[u("div",ee,[e(V)])]),_:1})]),_:1})]),_:1})])}}}),oe=Q(te,[["__scopeId","data-v-aac0d855"]]);export{oe as default};
