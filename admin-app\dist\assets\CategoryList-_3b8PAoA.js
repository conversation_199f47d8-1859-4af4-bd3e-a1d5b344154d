import{d as Te,r as k,a as q,a3 as Ae,o as Se,p as w,c as C,e as s,f as t,w as l,j as c,n as d,h as y,F as Y,g as Ue,H as Fe,I as Ne,a4 as Z,a5 as Q,Y as $e,J as N,P as T,Z as R,t as _,D as Be,U as De,a6 as ze,S as W,T as X,C as ee,a7 as Ee,a8 as Me,a9 as He,aa as <PERSON>,ab as he,ac as je,ad as Le,ae as te,k as f,_ as Je}from"./index-BDkHONMN.js";import{_ as Pe}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ge={class:"category-list-page"},Ke={class:"page-header"},qe={class:"header-actions"},Ye={class:"tree-node"},Ze={class:"node-content"},Qe={class:"node-info"},We={class:"node-label"},Xe={class:"node-count"},et={class:"node-actions"},tt={key:0,class:"node-meta"},lt={class:"node-desc"},ot={class:"icon-selector"},at={key:1},nt={class:"dialog-footer"},st={class:"icon-grid"},dt=["onClick"],rt={class:"icon-name"},it={class:"dialog-footer"},ut={key:0,class:"category-detail"},ct={class:"detail-section"},pt={key:0,class:"detail-section"},mt={key:1,class:"detail-section"},_t={class:"keywords"},ft={class:"dialog-footer"},vt=Te({__name:"CategoryList",setup(yt){const $=k(!1),B=k(!1),b=k(!1),A=k(!1),I=k(!1),S=k(),D=k(),U=k([]),m=k(null),V=q({keyword:"",status:null}),r=q({id:null,name:"",description:"",parentId:null,icon:"HomeIcon",sortOrder:0,status:1,keywords:""}),le={name:[{required:!0,message:"请输入分类名称",trigger:"blur"},{min:2,max:20,message:"分类名称长度为2-20个字符",trigger:"blur"}],description:[{max:200,message:"描述不能超过200个字符",trigger:"blur"}]},h={children:"children",label:"name",value:"id"},oe=["HomeIcon","BowlIcon","CoffeeIcon","AppleIcon","FishIcon","GrapeIcon","OrangeIcon","CherryIcon","FolderOpened","Folder","DocumentCopy","Star","Flag"],ae=Ae(()=>{const o=e=>e.filter(n=>r.id&&n.id===r.id?!1:(n.children&&(n.children=o(n.children)),!0));return o(JSON.parse(JSON.stringify(U.value)))});Se(()=>{x()});const x=async()=>{$.value=!0;try{U.value=[{id:1,name:"家常菜",description:"日常家庭烹饪的菜品",parentId:null,icon:"HomeIcon",sortOrder:1,status:1,recipeCount:125,keywords:"家常,日常,简单",createdAt:"2024-01-15T10:30:00",updatedAt:"2024-01-20T14:20:00",children:[{id:11,name:"荤菜",description:"以肉类为主的菜品",parentId:1,parentName:"家常菜",icon:"BowlIcon",sortOrder:1,status:1,recipeCount:85,keywords:"肉,荤菜",createdAt:"2024-01-15T11:00:00",updatedAt:"2024-01-20T14:20:00"},{id:12,name:"素菜",description:"以蔬菜为主的菜品",parentId:1,parentName:"家常菜",icon:"AppleIcon",sortOrder:2,status:1,recipeCount:40,keywords:"蔬菜,素食",createdAt:"2024-01-15T11:30:00",updatedAt:"2024-01-20T14:20:00"}]},{id:2,name:"甜品",description:"各种糕点和甜品制作",parentId:null,icon:"CherryIcon",sortOrder:2,status:1,recipeCount:65,keywords:"甜品,糕点,烘焙",createdAt:"2024-01-16T09:00:00",updatedAt:"2024-01-20T14:20:00"}]}catch{w.error("加载分类失败")}finally{$.value=!1}},z=()=>{x()},ne=()=>{Object.assign(V,{keyword:"",status:null}),z()},se=()=>{if(S.value){const o=U.value,e=n=>{n.forEach(i=>{var p,g;const v=(g=(p=S.value)==null?void 0:p.store)==null?void 0:g.nodesMap[i.id];v&&(v.expanded=!0),i.children&&i.children.length>0&&e(i.children)})};e(o)}},de=()=>{if(S.value){const o=U.value,e=n=>{n.forEach(i=>{var p,g;const v=(g=(p=S.value)==null?void 0:p.store)==null?void 0:g.nodesMap[i.id];v&&(v.expanded=!1),i.children&&i.children.length>0&&e(i.children)})};e(o)}},re=()=>{j(),b.value=!0},ie=o=>{j(),r.parentId=o.id,b.value=!0},E=o=>{o&&(Object.assign(r,{id:o.id,name:o.name,description:o.description,parentId:o.parentId,icon:o.icon,sortOrder:o.sortOrder,status:o.status,keywords:o.keywords}),b.value=!0,A.value=!1)},ue=o=>{m.value=o,A.value=!0},j=()=>{Object.assign(r,{id:null,name:"",description:"",parentId:null,icon:"HomeIcon",sortOrder:0,status:1,keywords:""})},ce=async()=>{if(D.value)try{await D.value.validate(),B.value=!0,await new Promise(o=>setTimeout(o,1e3)),w.success(r.id?"编辑成功":"添加成功"),b.value=!1,x()}catch(o){console.error("表单验证失败:",o)}finally{B.value=!1}},pe=async o=>{const{action:e,category:n}=o;switch(e){case"toggleStatus":await me(n);break;case"moveUp":await L();break;case"moveDown":await L();break;case"setIcon":_e(n);break;case"delete":await fe();break}},me=async o=>{const e=o.status===1?"禁用":"启用";try{await ee.confirm(`确定要${e}此分类吗？`,"确认操作",{type:"warning"}),w.success(`${e}成功`),x()}catch{}},L=async(o,e)=>{try{w.success("移动成功"),x()}catch{w.error("移动失败")}},_e=o=>{E(o),I.value=!0},fe=async o=>{try{await ee.confirm("确定要删除此分类吗？删除后不可恢复！","危险操作",{type:"error",confirmButtonText:"确认删除",confirmButtonClass:"el-button--danger"}),w.success("删除成功"),x()}catch{}},ve=(o,e,n)=>{console.log("拖拽操作:",o,e,n),w.success("排序更新成功")},ye=o=>{r.icon=o},ge=o=>({HomeIcon:te,BowlIcon:Le,CoffeeIcon:je,AppleIcon:he,FishIcon:Re,GrapeIcon:He,OrangeIcon:Me,CherryIcon:Ee,FolderOpened:Z,Folder:Q})[o.icon]||te,ke=o=>new Date(o).toLocaleString("zh-CN");return(o,e)=>{const n=c("el-icon"),i=c("el-button"),v=c("el-input"),p=c("el-form-item"),g=c("el-option"),Ce=c("el-select"),J=c("el-form"),P=c("el-card"),M=c("el-tag"),F=c("el-dropdown-item"),be=c("el-dropdown-menu"),we=c("el-dropdown"),Ie=c("el-tree-select"),Ve=c("el-input-number"),G=c("el-radio"),xe=c("el-radio-group"),H=c("el-dialog"),O=c("el-descriptions-item"),Oe=c("el-descriptions");return f(),C("div",Ge,[s("div",Ke,[e[19]||(e[19]=s("h2",null,"分类管理",-1)),e[20]||(e[20]=s("p",null,"管理食谱分类的层级结构和属性",-1)),s("div",qe,[t(i,{type:"primary",onClick:re},{default:l(()=>[t(n,null,{default:l(()=>[t(y(Y))]),_:1}),e[18]||(e[18]=d(" 添加分类 "))]),_:1,__:[18]})])]),t(P,{class:"toolbar-card"},{default:l(()=>[t(J,{model:V,inline:!0,class:"search-form"},{default:l(()=>[t(p,{label:"关键词"},{default:l(()=>[t(v,{modelValue:V.keyword,"onUpdate:modelValue":e[0]||(e[0]=a=>V.keyword=a),placeholder:"搜索分类名称、描述",clearable:"",style:{width:"200px"},onKeyup:Ue(z,["enter"])},null,8,["modelValue"])]),_:1}),t(p,{label:"状态"},{default:l(()=>[t(Ce,{modelValue:V.status,"onUpdate:modelValue":e[1]||(e[1]=a=>V.status=a),placeholder:"选择状态",clearable:""},{default:l(()=>[t(g,{label:"全部状态",value:null}),t(g,{label:"启用",value:1}),t(g,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),t(p,null,{default:l(()=>[t(i,{type:"primary",onClick:z,loading:$.value},{default:l(()=>[t(n,null,{default:l(()=>[t(y(Fe))]),_:1}),e[21]||(e[21]=d(" 搜索 "))]),_:1,__:[21]},8,["loading"]),t(i,{onClick:ne},{default:l(()=>[t(n,null,{default:l(()=>[t(y(Ne))]),_:1}),e[22]||(e[22]=d(" 重置 "))]),_:1,__:[22]}),t(i,{onClick:se},{default:l(()=>[t(n,null,{default:l(()=>[t(y(Z))]),_:1}),e[23]||(e[23]=d(" 展开全部 "))]),_:1,__:[23]}),t(i,{onClick:de},{default:l(()=>[t(n,null,{default:l(()=>[t(y(Q))]),_:1}),e[24]||(e[24]=d(" 收起全部 "))]),_:1,__:[24]})]),_:1})]),_:1},8,["model"])]),_:1}),t(P,{class:"tree-card"},{default:l(()=>[e[29]||(e[29]=s("div",{class:"tree-header"},[s("span",null,"分类结构"),s("span",{class:"tree-tips"},"拖拽分类可以调整排序")],-1)),t(y($e),{ref_key:"categoryTreeRef",ref:S,data:U.value,props:h,"node-key":"id","default-expand-all":!1,"expand-on-click-node":!1,"check-on-click-node":!1,"render-after-expand":!1,draggable:"",onNodeDrop:ve,class:"category-tree"},{default:l(({node:a,data:u})=>[s("div",Ye,[s("div",Ze,[s("div",Qe,[t(n,{class:"node-icon"},{default:l(()=>[(f(),T(R(ge(u))))]),_:2},1024),s("span",We,_(u.name),1),t(M,{type:u.status===1?"success":"danger",size:"small",class:"node-status"},{default:l(()=>[d(_(u.status===1?"启用":"禁用"),1)]),_:2},1032,["type"]),s("span",Xe,"("+_(u.recipeCount||0)+"个食谱)",1)]),s("div",et,[t(i,{type:"text",size:"small",onClick:K=>ue(u)},{default:l(()=>[t(n,null,{default:l(()=>[t(y(Be))]),_:1})]),_:2},1032,["onClick"]),t(i,{type:"text",size:"small",onClick:K=>E(u)},{default:l(()=>[t(n,null,{default:l(()=>[t(y(De))]),_:1})]),_:2},1032,["onClick"]),t(i,{type:"text",size:"small",onClick:K=>ie(u)},{default:l(()=>[t(n,null,{default:l(()=>[t(y(Y))]),_:1})]),_:2},1032,["onClick"]),t(we,{onCommand:pe,trigger:"click"},{dropdown:l(()=>[t(be,null,{default:l(()=>[t(F,{command:{action:"toggleStatus",category:u}},{default:l(()=>[d(_(u.status===1?"禁用分类":"启用分类"),1)]),_:2},1032,["command"]),t(F,{command:{action:"moveUp",category:u},disabled:!1},{default:l(()=>e[25]||(e[25]=[d(" 向上移动 ")])),_:2,__:[25]},1032,["command","disabled"]),t(F,{command:{action:"moveDown",category:u},disabled:!1},{default:l(()=>e[26]||(e[26]=[d(" 向下移动 ")])),_:2,__:[26]},1032,["command","disabled"]),t(F,{command:{action:"setIcon",category:u},divided:""},{default:l(()=>e[27]||(e[27]=[d(" 设置图标 ")])),_:2,__:[27]},1032,["command"]),t(F,{command:{action:"delete",category:u},disabled:u.recipeCount>0||u.children&&u.children.length>0},{default:l(()=>e[28]||(e[28]=[d(" 删除分类 ")])),_:2,__:[28]},1032,["command","disabled"])]),_:2},1024)]),default:l(()=>[t(i,{type:"text",size:"small"},{default:l(()=>[t(n,null,{default:l(()=>[t(y(ze))]),_:1})]),_:1})]),_:2},1024)])]),u.description?(f(),C("div",tt,[s("span",lt,_(u.description),1)])):N("",!0)])]),_:1},8,["data"])]),_:1,__:[29]}),t(H,{modelValue:b.value,"onUpdate:modelValue":e[11]||(e[11]=a=>b.value=a),title:r.id?"编辑分类":"添加分类",width:"600px"},{footer:l(()=>[s("span",nt,[t(i,{onClick:e[10]||(e[10]=a=>b.value=!1)},{default:l(()=>e[34]||(e[34]=[d("取消")])),_:1,__:[34]}),t(i,{type:"primary",onClick:ce,loading:B.value},{default:l(()=>e[35]||(e[35]=[d(" 保存 ")])),_:1,__:[35]},8,["loading"])])]),default:l(()=>[t(J,{ref_key:"editFormRef",ref:D,model:r,rules:le,"label-width":"100px"},{default:l(()=>[t(p,{label:"分类名称",prop:"name"},{default:l(()=>[t(v,{modelValue:r.name,"onUpdate:modelValue":e[2]||(e[2]=a=>r.name=a),placeholder:"请输入分类名称"},null,8,["modelValue"])]),_:1}),t(p,{label:"分类描述",prop:"description"},{default:l(()=>[t(v,{modelValue:r.description,"onUpdate:modelValue":e[3]||(e[3]=a=>r.description=a),type:"textarea",rows:3,placeholder:"请输入分类描述"},null,8,["modelValue"])]),_:1}),t(p,{label:"父级分类",prop:"parentId"},{default:l(()=>[t(Ie,{modelValue:r.parentId,"onUpdate:modelValue":e[4]||(e[4]=a=>r.parentId=a),data:ae.value,props:h,placeholder:"选择父级分类（可选）",clearable:"","check-strictly":"","node-key":"id"},null,8,["modelValue","data"])]),_:1}),t(p,{label:"分类图标",prop:"icon"},{default:l(()=>[s("div",ot,[t(v,{modelValue:r.icon,"onUpdate:modelValue":e[5]||(e[5]=a=>r.icon=a),placeholder:"图标名称",readonly:""},{prepend:l(()=>[r.icon?(f(),T(n,{key:0},{default:l(()=>[(f(),T(R(r.icon)))]),_:1})):(f(),C("span",at,"图标"))]),_:1},8,["modelValue"]),t(i,{onClick:e[6]||(e[6]=a=>I.value=!0),style:{"margin-left":"8px"}},{default:l(()=>e[30]||(e[30]=[d(" 选择图标 ")])),_:1,__:[30]})])]),_:1}),t(p,{label:"排序值",prop:"sortOrder"},{default:l(()=>[t(Ve,{modelValue:r.sortOrder,"onUpdate:modelValue":e[7]||(e[7]=a=>r.sortOrder=a),min:0,max:999,"controls-position":"right"},null,8,["modelValue"]),e[31]||(e[31]=s("span",{class:"form-tip"},"数值越小排序越靠前",-1))]),_:1,__:[31]}),t(p,{label:"状态",prop:"status"},{default:l(()=>[t(xe,{modelValue:r.status,"onUpdate:modelValue":e[8]||(e[8]=a=>r.status=a)},{default:l(()=>[t(G,{label:1},{default:l(()=>e[32]||(e[32]=[d("启用")])),_:1,__:[32]}),t(G,{label:0},{default:l(()=>e[33]||(e[33]=[d("禁用")])),_:1,__:[33]})]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"SEO关键词",prop:"keywords"},{default:l(()=>[t(v,{modelValue:r.keywords,"onUpdate:modelValue":e[9]||(e[9]=a=>r.keywords=a),placeholder:"多个关键词用逗号分隔"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(H,{modelValue:I.value,"onUpdate:modelValue":e[14]||(e[14]=a=>I.value=a),title:"选择图标",width:"800px"},{footer:l(()=>[s("span",it,[t(i,{onClick:e[12]||(e[12]=a=>I.value=!1)},{default:l(()=>e[36]||(e[36]=[d("取消")])),_:1,__:[36]}),t(i,{type:"primary",onClick:e[13]||(e[13]=a=>I.value=!1)},{default:l(()=>e[37]||(e[37]=[d(" 确定 ")])),_:1,__:[37]})])]),default:l(()=>[s("div",st,[(f(),C(W,null,X(oe,a=>s("div",{key:a,class:Je(["icon-item",{active:r.icon===a}]),onClick:u=>ye(a)},[t(n,{size:24},{default:l(()=>[(f(),T(R(a)))]),_:2},1024),s("span",rt,_(a),1)],10,dt)),64))])]),_:1},8,["modelValue"]),t(H,{modelValue:A.value,"onUpdate:modelValue":e[17]||(e[17]=a=>A.value=a),title:"分类详情",width:"600px"},{footer:l(()=>[s("span",ft,[t(i,{onClick:e[15]||(e[15]=a=>A.value=!1)},{default:l(()=>e[41]||(e[41]=[d("关闭")])),_:1,__:[41]}),m.value?(f(),T(i,{key:0,type:"primary",onClick:e[16]||(e[16]=a=>E(m.value))},{default:l(()=>e[42]||(e[42]=[d(" 编辑分类 ")])),_:1,__:[42]})):N("",!0)])]),default:l(()=>[m.value?(f(),C("div",ut,[s("div",ct,[e[38]||(e[38]=s("h4",null,"基本信息",-1)),t(Oe,{column:2,border:""},{default:l(()=>[t(O,{label:"分类名称"},{default:l(()=>[d(_(m.value.name),1)]),_:1}),t(O,{label:"状态"},{default:l(()=>[t(M,{type:m.value.status===1?"success":"danger"},{default:l(()=>[d(_(m.value.status===1?"启用":"禁用"),1)]),_:1},8,["type"])]),_:1}),t(O,{label:"父级分类"},{default:l(()=>[d(_(m.value.parentName||"无"),1)]),_:1}),t(O,{label:"排序值"},{default:l(()=>[d(_(m.value.sortOrder),1)]),_:1}),t(O,{label:"食谱数量"},{default:l(()=>[d(_(m.value.recipeCount||0),1)]),_:1}),t(O,{label:"创建时间"},{default:l(()=>[d(_(ke(m.value.createdAt)),1)]),_:1})]),_:1})]),m.value.description?(f(),C("div",pt,[e[39]||(e[39]=s("h4",null,"分类描述",-1)),s("p",null,_(m.value.description),1)])):N("",!0),m.value.keywords?(f(),C("div",mt,[e[40]||(e[40]=s("h4",null,"SEO关键词",-1)),s("div",_t,[(f(!0),C(W,null,X(m.value.keywords.split(","),a=>(f(),T(M,{key:a.trim(),size:"small"},{default:l(()=>[d(_(a.trim()),1)]),_:2},1024))),128))])])):N("",!0)])):N("",!0)]),_:1},8,["modelValue"])])}}}),Ct=Pe(vt,[["__scopeId","data-v-13364688"]]);export{Ct as default};
