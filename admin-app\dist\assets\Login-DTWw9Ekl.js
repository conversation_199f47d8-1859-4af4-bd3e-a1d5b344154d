import{d as k,u as h,r as _,a as M,o as R,b as C,c as E,e as s,f as r,w as a,g as F,h as m,E as U,i as q,j as u,k as z,l as B,m as L,n as b,t as N,p as v}from"./index-BDkHONMN.js";import{_ as S}from"./_plugin-vue_export-helper-DlAUqK2U.js";const A={class:"login-container"},K={class:"login-content"},j={class:"login-form-container"},D=k({__name:"Login",setup(I){const p=C(),w=q(),c=h(),d=_(),t=_(!1),o=M({username:"",password:"",rememberMe:!1}),x={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}]},g=async()=>{if(d.value)try{await d.value.validate(),t.value=!0,await c.login({username:o.username,password:o.password}),v.success("登录成功");const n=w.query.redirect||"/dashboard";p.push(n)}catch(n){console.error("登录失败:",n),v.error("登录失败，请检查用户名和密码")}finally{t.value=!1}};return R(()=>{c.isAuthenticated()&&p.push("/dashboard"),o.username="admin",o.password="admin123"}),(n,e)=>{const f=u("el-input"),i=u("el-form-item"),y=u("el-checkbox"),V=u("el-button");return z(),E("div",A,[e[6]||(e[6]=s("div",{class:"login-background"},[s("div",{class:"background-overlay"})],-1)),s("div",K,[s("div",j,[e[4]||(e[4]=s("div",{class:"login-header"},[s("h1",{class:"login-title"},"食谱管理系统"),s("p",{class:"login-subtitle"},"管理员登录")],-1)),r(m(U),{ref_key:"loginFormRef",ref:d,model:o,rules:x,class:"login-form",onKeyup:F(g,["enter"])},{default:a(()=>[r(i,{prop:"username"},{default:a(()=>[r(f,{modelValue:o.username,"onUpdate:modelValue":e[0]||(e[0]=l=>o.username=l),type:"text",placeholder:"请输入用户名",size:"large",clearable:"","prefix-icon":m(B)},null,8,["modelValue","prefix-icon"])]),_:1}),r(i,{prop:"password"},{default:a(()=>[r(f,{modelValue:o.password,"onUpdate:modelValue":e[1]||(e[1]=l=>o.password=l),type:"password",placeholder:"请输入密码",size:"large","show-password":"",clearable:"","prefix-icon":m(L)},null,8,["modelValue","prefix-icon"])]),_:1}),r(i,null,{default:a(()=>[r(y,{modelValue:o.rememberMe,"onUpdate:modelValue":e[2]||(e[2]=l=>o.rememberMe=l)},{default:a(()=>e[3]||(e[3]=[b(" 记住我 ")])),_:1,__:[3]},8,["modelValue"])]),_:1}),r(i,null,{default:a(()=>[r(V,{type:"primary",size:"large",loading:t.value,class:"login-button",onClick:g},{default:a(()=>[b(N(t.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),e[5]||(e[5]=s("div",{class:"login-footer"},[s("p",{class:"copyright"}," © 2024 个人食谱管理系统. All rights reserved. ")],-1))])])])}}}),H=S(D,[["__scopeId","data-v-bb536598"]]);export{H as default};
