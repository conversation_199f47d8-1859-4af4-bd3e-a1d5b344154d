import request from '@/utils/request'

// 首页统计数据类型定义
export interface GlobalStats {
  totalUsers: number
  totalRecipes: number
  totalViews: number
  totalLikes: number
  totalFavorites: number
  todayNewUsers: number
  todayNewRecipes: number
  activeUsers: number
}

export interface UserStats {
  myRecipeCount: number
  myFavoriteCount: number
  myFollowingCount: number
  myFollowersCount: number
  myTotalViews: number
  myTotalLikes: number
}

export interface HomeStats {
  globalStats: GlobalStats
  userStats?: UserStats
}

// 最新食谱类型定义
export interface LatestRecipe {
  id: number
  title: string
  description: string
  coverImage: string
  prepTime: number
  cookTime: number
  difficulty: number
  servings: number
  views: number
  likes: number
  favorites: number
  categoryId: number
  categoryName: string
  creatorId: number
  creatorUsername: string
  creatorNickname: string
  creatorAvatar: string
  tags: string
  createdAt: string
  updatedAt: string
}

// 热门创作者类型定义
export interface PopularCreator {
  id: number
  username: string
  nickname: string
  avatar: string
  bio: string
  recipeCount: number
  totalViews: number
  totalLikes: number
  totalFavorites: number
  followersCount: number
  followingCount: number
  latestRecipeTitle: string
  latestRecipeCover: string
  latestPublishTime: string
  isFollowed: boolean
}

// 首页API
export const homeApi = {
  /**
   * 获取首页统计数据
   */
  getHomeStats(): Promise<HomeStats> {
    return request.get('/api/home/<USER>')
  },

  /**
   * 获取最新发布的食谱
   * @param limit 限制数量，默认8条
   */
  getLatestRecipes(limit: number = 8): Promise<LatestRecipe[]> {
    return request.get('/api/home/<USER>', {
      params: { limit }
    })
  },

  /**
   * 获取热门创作者
   * @param limit 限制数量，默认6条
   */
  getPopularCreators(limit: number = 6): Promise<PopularCreator[]> {
    return request.get('/api/home/<USER>', {
      params: { limit }
    })
  },

  /**
   * 获取推荐食谱
   * @param limit 限制数量，默认8条
   */
  getRecommendedRecipes(limit: number = 8): Promise<LatestRecipe[]> {
    return request.get('/api/home/<USER>', {
      params: { limit }
    })
  },

  /**
   * 获取热门搜索关键词
   * @param limit 限制数量，默认10条
   */
  getHotSearchKeywords(limit: number = 10): Promise<string[]> {
    return request.get('/api/home/<USER>', {
      params: { limit }
    })
  }
}
