<template>
  <div class="popular-recipes-view">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <el-icon><Star /></el-icon>
            热门食谱
          </h1>
          <p class="page-subtitle">探索最受欢迎的美食食谱，跟着大厨学做菜</p>
        </div>
        
        <!-- 筛选标签 -->
        <div class="filter-tabs">
          <el-tabs v-model="activeTab" @tab-click="handleTabChange">
            <el-tab-pane label="本周热门" name="week"></el-tab-pane>
            <el-tab-pane label="本月热门" name="month"></el-tab-pane>
            <el-tab-pane label="最多点赞" name="likes"></el-tab-pane>
            <el-tab-pane label="最多收藏" name="favorites"></el-tab-pane>
            <el-tab-pane label="最多浏览" name="views"></el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 热门统计卡片 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><Dish /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ totalRecipes }}</div>
                <div class="stat-label">热门食谱</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><View /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ totalViews }}</div>
                <div class="stat-label">总浏览量</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><Star /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ totalLikes }}</div>
                <div class="stat-label">总点赞数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><Collection /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ totalFavorites }}</div>
                <div class="stat-label">总收藏数</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 排行榜 -->
      <div class="ranking-section">
        <h2 class="section-title">
          <el-icon><Trophy /></el-icon>
          食谱排行榜
        </h2>
        
        <div class="ranking-content">
          <div class="ranking-list">
            <div
              v-for="(recipe, index) in popularRecipes"
              :key="recipe.id"
              class="ranking-item"
              @click="goToRecipeDetail(recipe.id)"
            >
              <div class="ranking-number" :class="getRankingClass(index)">
                {{ index + 1 }}
              </div>
              
              <div class="recipe-cover">
                <el-image
                  :src="getFileUrl(recipe.coverImage) || '/default-recipe.jpg'"
                  :alt="recipe.title"
                  fit="cover"
                  class="cover-image"
                />
                <div class="difficulty-badge" :class="`difficulty-${recipe.difficulty}`">
                  {{ getDifficultyText(recipe.difficulty) }}
                </div>
              </div>
              
              <div class="recipe-info">
                <h3 class="recipe-title">{{ recipe.title }}</h3>
                <p class="recipe-description">{{ recipe.description }}</p>
                
                <div class="recipe-meta">
                  <div class="meta-item">
                    <el-icon><Timer /></el-icon>
                    <span>{{ getTotalTime(recipe.prepTime, recipe.cookTime) }}分钟</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><User /></el-icon>
                    <span>{{ recipe.servings }}人份</span>
                  </div>
                </div>
                
                <div class="recipe-tags" v-if="recipe.tags">
                  <el-tag
                    v-for="tag in getTagList(recipe.tags)"
                    :key="tag"
                    size="small"
                    class="recipe-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
              
              <div class="recipe-stats">
                <div class="stat-item">
                  <el-icon><View /></el-icon>
                  <span>{{ formatNumber(recipe.views || 0) }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Star /></el-icon>
                  <span>{{ formatNumber(recipe.likes || 0) }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Collection /></el-icon>
                  <span>{{ formatNumber(recipe.favorites || 0) }}</span>
                </div>
                <div class="rating-display">
                  <el-rate
                    v-model="recipe.favorites"
                    disabled
                    show-score
                    text-color="#ff9900"
                    score-template="{value}"
                  />
                </div>
              </div>
              
              <div class="recipe-author">
                <el-avatar :size="32" :src="getFileUrl(recipe.creatorAvatar)">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="author-name">{{ recipe.creatorName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div class="loading-section" v-if="loading">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 空状态 -->
      <div class="empty-section" v-if="!loading && popularRecipes.length === 0">
        <el-empty description="暂无热门食谱">
          <el-button type="primary" @click="$router.push('/recipe/create')">
            创建第一个食谱
          </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Star,
  Dish,
  View,
  Collection,
  Trophy,
  Timer,
  User
} from '@element-plus/icons-vue'
import type { Recipe } from '@/types/recipe'
import { recipesApi } from '@/api/recipes'
import { getFileUrl } from '@/utils/file'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeTab = ref('week')
const popularRecipes = ref<Recipe[]>([])

// 统计数据
const totalRecipes = ref(0)
const totalViews = ref(0)
const totalLikes = ref(0)
const totalFavorites = ref(0)

// 页面初始化
onMounted(() => {
  loadPopularRecipes()
  loadStats()
})

/**
 * 加载热门食谱
 */
const loadPopularRecipes = async () => {
  loading.value = true
  
  try {
    const response = await recipesApi.getPopularRanking(activeTab.value, 20)
    popularRecipes.value = response.data
    
  } catch (error) {
    ElMessage.error('加载热门食谱失败')
    console.error('加载热门食谱失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 加载统计数据
 */
const loadStats = async () => {
  try {
    const response = await recipesApi.getPopularStats()
    const stats = response.data
    totalRecipes.value = stats.totalRecipes
    totalViews.value = stats.totalViews
    totalLikes.value = stats.totalLikes
    totalFavorites.value = stats.totalFavorites
    
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 如果API失败，使用默认值
    totalRecipes.value = 0
    totalViews.value = 0
    totalLikes.value = 0
    totalFavorites.value = 0
  }
}

/**
 * 切换标签
 */
const handleTabChange = (tab: any) => {
  activeTab.value = tab.props.name
  loadPopularRecipes()
}

/**
 * 跳转到食谱详情
 */
const goToRecipeDetail = (id: number) => {
  router.push(`/recipe/${id}`)
}

/**
 * 获取排名样式类
 */
const getRankingClass = (index: number) => {
  if (index === 0) return 'rank-first'
  if (index === 1) return 'rank-second'
  if (index === 2) return 'rank-third'
  return 'rank-normal'
}

/**
 * 获取难度文本
 */
const getDifficultyText = (difficulty: number) => {
  const difficultyMap = { 1: '简单', 2: '中等', 3: '困难' }
  return difficultyMap[difficulty as keyof typeof difficultyMap] || '未知'
}

/**
 * 获取总时间
 */
const getTotalTime = (prepTime: number, cookTime: number) => {
  return (prepTime || 0) + (cookTime || 0)
}

/**
 * 获取标签列表
 */
const getTagList = (tags: string) => {
  return tags ? tags.split(',').slice(0, 3) : []
}

/**
 * 格式化数字
 */
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

/**
 * 生成模拟数据
 */
const generateMockRecipes = (): Recipe[] => {
  return Array.from({ length: 20 }, (_, index) => ({
    id: index + 1,
    title: `热门食谱 ${index + 1}`,
    description: '这是一个非常受欢迎的食谱，制作简单，口感极佳...',
    coverImage: `/mock-recipe-${(index % 6) + 1}.jpg`,
    prepTime: 15 + index * 5,
    cookTime: 30 + index * 10,
    difficulty: (index % 3) + 1,
    servings: 2 + (index % 4),
    tags: ['家常菜', '快手菜', '下饭菜'].slice(0, (index % 3) + 1).join(','),
    views: Math.floor(Math.random() * 10000) + 1000,
    likes: Math.floor(Math.random() * 1000) + 100,
    favorites: Math.floor(Math.random() * 500) + 50,
    averageRating: Number((3.5 + Math.random() * 1.5).toFixed(1)),
    creatorName: `美食达人${index + 1}`,
    creatorAvatar: `/mock-avatar-${(index % 5) + 1}.jpg`,
    categoryId: (index % 5) + 1,
    creatorId: (index % 10) + 1,
    isPublic: 1,
    status: 1,
    createdAt: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString()
  }))
}
</script>

<style scoped>
.popular-recipes-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  background: var(--bg-primary);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

.header-content {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2.5em;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 1.1em;
  color: var(--text-secondary);
  margin: 0;
}

.filter-tabs {
  :deep(.el-tabs__nav-wrap) {
    background: transparent;
  }
  
  :deep(.el-tabs__item) {
    font-weight: 600;
    color: var(--text-secondary);
    transition: all 0.3s ease;
  }
  
  :deep(.el-tabs__item.is-active) {
    color: var(--primary-color);
  }
  
  :deep(.el-tabs__item:hover) {
    color: var(--primary-light);
  }
}

.stats-section {
  margin-bottom: 30px;
}

.stat-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-md);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-light);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius-md);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.stat-value {
  font-size: 1.8em;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.9em;
  color: var(--text-secondary);
  margin-top: 5px;
}

.ranking-section {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: 30px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

.section-title {
  font-size: 1.5em;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border-radius: var(--border-radius-md);
  margin-bottom: 15px;
  background: var(--bg-light);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ranking-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 154, 162, 0.05), rgba(181, 234, 215, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.ranking-item:hover {
  background: var(--bg-primary);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-light);
  transform: translateY(-2px);
}

.ranking-item:hover::before {
  opacity: 1;
}

.ranking-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2em;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.rank-first {
  background: linear-gradient(135deg, #ffd700, var(--primary-color));
  animation: glow-gold 2s infinite ease-in-out;
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0, var(--primary-light));
  animation: glow-silver 2s infinite ease-in-out;
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32, var(--accent-color));
  animation: glow-bronze 2s infinite ease-in-out;
}

.rank-normal {
  background: linear-gradient(135deg, var(--text-secondary), var(--text-light));
}

@keyframes glow-gold {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 4px 16px rgba(255, 215, 0, 0.6);
  }
}

@keyframes glow-silver {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(192, 192, 192, 0.3);
  }
  50% {
    box-shadow: 0 4px 16px rgba(192, 192, 192, 0.6);
  }
}

@keyframes glow-bronze {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(205, 127, 50, 0.3);
  }
  50% {
    box-shadow: 0 4px 16px rgba(205, 127, 50, 0.6);
  }
}

.recipe-cover {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.cover-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.ranking-item:hover .cover-image {
  transform: scale(1.05);
}

.difficulty-badge {
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 0.7em;
  font-weight: 600;
  color: white;
  backdrop-filter: blur(4px);
}

.difficulty-1 {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.difficulty-2 {
  background: linear-gradient(135deg, #e6a23c, #eeb563);
}

.difficulty-3 {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.recipe-info {
  flex: 1;
}

.recipe-title {
  font-size: 1.2em;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.recipe-description {
  color: var(--text-secondary);
  font-size: 0.9em;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--text-secondary);
  font-size: 0.9em;
}

.recipe-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.recipe-tag {
  font-size: 0.8em;
  background: linear-gradient(135deg, var(--primary-light), var(--accent-color));
  color: var(--text-primary);
  border: none;
}

.recipe-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  min-width: 80px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--text-secondary);
  font-size: 0.9em;
  padding: 4px 8px;
  border-radius: 12px;
  background: var(--bg-light);
  transition: all 0.3s ease;
}

.stat-item:hover {
  color: var(--primary-color);
  background: var(--accent-color);
}

.rating-display {
  :deep(.el-rate) {
    --el-rate-icon-size: 14px;
    --el-rate-icon-color: var(--warning-color);
    --el-rate-text-color: var(--text-secondary);
  }
}

.recipe-author {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 60px;
}

.author-name {
  font-size: 0.8em;
  color: var(--text-secondary);
  text-align: center;
}

.loading-section,
.empty-section {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: 40px;
  text-align: center;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ranking-item {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.ranking-item:nth-child(1) { animation-delay: 0.1s; }
.ranking-item:nth-child(2) { animation-delay: 0.2s; }
.ranking-item:nth-child(3) { animation-delay: 0.3s; }
.ranking-item:nth-child(4) { animation-delay: 0.4s; }
.ranking-item:nth-child(5) { animation-delay: 0.5s; }

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 15px;
  }
  
  .page-header {
    padding: 20px;
  }
  
  .page-title {
    font-size: 2em;
  }
  
  .ranking-item {
    flex-direction: column;
    text-align: center;
    gap: 15px;
    padding: 20px 15px;
  }
  
  .recipe-cover {
    width: 120px;
    height: 120px;
  }
  
  .recipe-stats {
    flex-direction: row;
    justify-content: center;
    min-width: auto;
  }
  
  .recipe-author {
    flex-direction: row;
    min-width: auto;
  }
  
  .stats-section .el-row {
    margin: 0 -10px;
  }
  
  .stats-section .el-col {
    padding: 0 10px;
    margin-bottom: 15px;
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-skeleton) {
  --el-skeleton-color: var(--bg-light);
  --el-skeleton-to-color: var(--border-color);
}

:deep(.el-empty__description) {
  color: var(--text-secondary);
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border: none;
  transition: all 0.3s ease;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}
</style> 