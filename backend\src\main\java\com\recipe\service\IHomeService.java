package com.recipe.service;

import com.recipe.dto.HomeStatsDTO;
import com.recipe.dto.LatestRecipeDTO;
import com.recipe.dto.PopularCreatorDTO;

import java.util.List;

/**
 * 首页服务接口
 */
public interface IHomeService {

    /**
     * 获取首页统计数据
     * @param userId 用户ID（可为空，未登录用户）
     * @return 首页统计数据
     */
    HomeStatsDTO getHomeStats(Long userId);

    /**
     * 获取最新发布的食谱
     * @param limit 限制数量
     * @return 最新食谱列表
     */
    List<LatestRecipeDTO> getLatestRecipes(Integer limit);

    /**
     * 获取热门创作者
     * @param userId 当前用户ID（用于判断关注状态，可为空）
     * @param limit 限制数量
     * @return 热门创作者列表
     */
    List<PopularCreatorDTO> getPopularCreators(Long userId, Integer limit);

    /**
     * 获取推荐食谱（基于用户喜好）
     * @param userId 用户ID（可为空）
     * @param limit 限制数量
     * @return 推荐食谱列表
     */
    List<LatestRecipeDTO> getRecommendedRecipes(Long userId, Integer limit);

    /**
     * 获取热门搜索关键词
     * @param limit 限制数量
     * @return 热门搜索关键词列表
     */
    List<String> getHotSearchKeywords(Integer limit);
}
