import{d as Me,r as k,a as z,o as Re,p as m,c as T,e as d,f as e,w as t,j as r,n,h as p,F as ze,G as Te,g as De,H as $e,I as Le,J as D,K as Be,t as c,L as Ee,M as Ne,B as Fe,N as J,O as Ye,P as $,Q as Ae,R as je,S as He,T as Oe,D as qe,s as Ke,U as Ge,V as Je,W as Qe,X as We,C as h,b as Xe,k as U}from"./index-BDkHONMN.js";import{_ as Ze}from"./_plugin-vue_export-helper-DlAUqK2U.js";const el={class:"user-list-page"},ll={class:"page-header"},tl={class:"header-actions"},al={key:0,class:"batch-toolbar"},sl={class:"selected-text"},ol={class:"batch-actions"},nl={class:"user-info"},dl={class:"user-details"},il={class:"user-name"},rl={class:"user-username"},ul={class:"user-email"},ml={class:"roles"},pl={class:"contact-info"},cl={key:0},fl={class:"location"},_l={class:"stats-info"},gl={class:"activity-info"},vl={class:"last-login"},bl={class:"login-ip"},Vl={class:"register-time"},yl={class:"actions"},wl={class:"pagination-wrapper"},kl={class:"dialog-footer"},Ul={class:"dialog-footer"},Cl=Me({__name:"UserList",setup(Il){const Q=Xe(),M=k(!1),L=k(!1),B=k(!1),x=k(!1),S=k(!1),E=k(),j=k([]),P=k([]),N=k(null),g=z({keyword:"",role:null,status:null,isVerified:null,dateRange:[]}),i=z({id:null,username:"",nickname:"",email:"",phone:"",password:"",roles:[],status:1,bio:""}),y=z({title:"",content:""}),W={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度为3-20个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度为6-20个字符",trigger:"blur"}]},b=z({page:1,pageSize:20,total:0});Re(()=>{v()});const v=async()=>{M.value=!0;try{j.value=[{id:1,username:"user001",nickname:"美食达人",email:"<EMAIL>",phone:"138****8888",avatar:"",bio:"热爱美食，喜欢分享各种家常菜做法",status:1,roles:["USER"],isVerified:!0,location:"北京市",registrationTime:"2024-01-15T10:30:00",lastLoginTime:"2024-01-20T15:20:00",lastLoginIp:"*************",recipeCount:25,followersCount:150,followingCount:89},{id:2,username:"vip_chef",nickname:"VIP厨师长",email:"<EMAIL>",phone:"139****9999",avatar:"",bio:"专业厨师，分享精品菜谱",status:1,roles:["USER","VIP"],isVerified:!0,location:"上海市",registrationTime:"2024-01-10T09:00:00",lastLoginTime:"2024-01-20T16:30:00",lastLoginIp:"*************",recipeCount:88,followersCount:520,followingCount:45}],b.total=2}catch{m.error("加载用户列表失败")}finally{M.value=!1}},F=()=>{b.page=1,v()},X=()=>{Object.assign(g,{keyword:"",role:null,status:null,isVerified:null,dateRange:[]}),F()},Z=s=>{P.value=s},H=async s=>{if(P.value.length===0){m.warning("请选择要操作的用户");return}const l=s===1?"启用":"禁用";try{await h.confirm(`确定要${l}选中的用户吗？`,"提示",{type:"warning"}),m.success(`${l}成功`),v()}catch{}},ee=async s=>{if(P.value.length===0){m.warning("请选择要操作的用户");return}try{await h.confirm(`确定要将选中用户设为${s}吗？`,"提示",{type:"warning"}),m.success("角色更新成功"),v()}catch{}},le=async()=>{if(P.value.length===0){m.warning("请选择要删除的用户");return}try{await h.confirm("确定要删除选中的用户吗？此操作不可逆！","危险操作",{type:"error",confirmButtonText:"确认删除",confirmButtonClass:"el-button--danger"}),m.success("删除成功"),v()}catch{}},te=()=>{ne(),x.value=!0},ae=async s=>{const{action:l,user:o}=s;switch(l){case"edit":oe(o);break;case"resetPassword":await ie();break;case"toggleStatus":await re(o);break;case"toggleRole":await ue(o);break;case"sendMessage":me(o);break;case"loginHistory":ce(o);break;case"delete":await fe();break}},se=s=>{Q.push(`/admin/users/${s}`)},oe=s=>{Object.assign(i,{id:s.id,username:s.username,nickname:s.nickname,email:s.email,phone:s.phone,password:"",roles:[...s.roles],status:s.status,bio:s.bio}),x.value=!0},ne=()=>{Object.assign(i,{id:null,username:"",nickname:"",email:"",phone:"",password:"",roles:["USER"],status:1,bio:""})},de=async()=>{if(E.value)try{await E.value.validate(),L.value=!0,await new Promise(s=>setTimeout(s,1e3)),m.success(i.id?"编辑成功":"添加成功"),x.value=!1,v()}catch(s){console.error("表单验证失败:",s)}finally{L.value=!1}},ie=async s=>{try{await h.confirm("确定要重置此用户的密码吗？","确认操作",{type:"warning"}),m.success("密码重置成功")}catch{}},re=async s=>{const l=s.status===1?"禁用":"启用";try{await h.confirm(`确定要${l}此用户吗？`,"确认操作",{type:"warning"}),m.success(`${l}成功`),v()}catch{}},ue=async s=>{const o=s.roles.includes("VIP")?"取消VIP":"设为VIP";try{await h.confirm(`确定要${o}吗？`,"确认操作",{type:"warning"}),m.success(`${o}成功`),v()}catch{}},me=s=>{N.value=s,y.title="",y.content="",S.value=!0},pe=async()=>{if(!y.title||!y.content){m.warning("请填写消息标题和内容");return}B.value=!0;try{await new Promise(s=>setTimeout(s,1e3)),m.success("消息发送成功"),S.value=!1}catch{m.error("消息发送失败")}finally{B.value=!1}},ce=s=>{m.info(`查看用户 ${s.username} 的登录历史`)},fe=async s=>{try{await h.confirm("确定要删除此用户吗？此操作将永久删除用户及其所有数据，且不可逆！","危险操作",{type:"error",confirmButtonText:"确认删除",confirmButtonClass:"el-button--danger"}),m.success("删除成功"),v()}catch{}},_e=()=>{m.success("导出成功")},ge=s=>{b.page=s,v()},ve=s=>{b.pageSize=s,b.page=1,v()},be=s=>({0:"danger",1:"success",2:"warning"})[s]||"info",Ve=s=>({0:"禁用",1:"正常",2:"待激活"})[s]||"未知",ye=s=>({USER:"",VIP:"warning",ADMIN:"danger"})[s]||"",we=s=>({USER:"普通用户",VIP:"VIP用户",ADMIN:"管理员"})[s]||s,O=s=>new Date(s).toLocaleString("zh-CN");return(s,l)=>{const o=r("el-icon"),f=r("el-button"),V=r("el-input"),u=r("el-form-item"),_=r("el-option"),R=r("el-select"),ke=r("el-date-picker"),Y=r("el-form"),q=r("el-card"),w=r("el-table-column"),Ue=r("el-avatar"),K=r("el-tag"),C=r("el-dropdown-item"),Ce=r("el-dropdown-menu"),Ie=r("el-dropdown"),he=r("el-table"),xe=r("el-pagination"),A=r("el-radio"),Pe=r("el-radio-group"),G=r("el-dialog"),Se=Ye("loading");return U(),T("div",el,[d("div",ll,[l[26]||(l[26]=d("h2",null,"用户管理",-1)),l[27]||(l[27]=d("p",null,"管理系统中的所有用户账户",-1)),d("div",tl,[e(f,{type:"primary",onClick:te},{default:t(()=>[e(o,null,{default:t(()=>[e(p(ze))]),_:1}),l[24]||(l[24]=n(" 添加用户 "))]),_:1,__:[24]}),e(f,{onClick:_e},{default:t(()=>[e(o,null,{default:t(()=>[e(p(Te))]),_:1}),l[25]||(l[25]=n(" 导出数据 "))]),_:1,__:[25]})])]),e(q,{class:"toolbar-card"},{default:t(()=>[e(Y,{model:g,inline:!0,class:"search-form"},{default:t(()=>[e(u,{label:"关键词"},{default:t(()=>[e(V,{modelValue:g.keyword,"onUpdate:modelValue":l[0]||(l[0]=a=>g.keyword=a),placeholder:"搜索用户名、昵称、邮箱",clearable:"",style:{width:"250px"},onKeyup:De(F,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"用户角色"},{default:t(()=>[e(R,{modelValue:g.role,"onUpdate:modelValue":l[1]||(l[1]=a=>g.role=a),placeholder:"选择角色",clearable:""},{default:t(()=>[e(_,{label:"全部角色",value:null}),e(_,{label:"普通用户",value:"USER"}),e(_,{label:"VIP用户",value:"VIP"}),e(_,{label:"管理员",value:"ADMIN"})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"账户状态"},{default:t(()=>[e(R,{modelValue:g.status,"onUpdate:modelValue":l[2]||(l[2]=a=>g.status=a),placeholder:"选择状态",clearable:""},{default:t(()=>[e(_,{label:"全部状态",value:null}),e(_,{label:"正常",value:1}),e(_,{label:"禁用",value:0}),e(_,{label:"待激活",value:2})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"认证状态"},{default:t(()=>[e(R,{modelValue:g.isVerified,"onUpdate:modelValue":l[3]||(l[3]=a=>g.isVerified=a),placeholder:"认证状态",clearable:""},{default:t(()=>[e(_,{label:"全部",value:null}),e(_,{label:"已认证",value:!0}),e(_,{label:"未认证",value:!1})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"注册时间"},{default:t(()=>[e(ke,{modelValue:g.dateRange,"onUpdate:modelValue":l[4]||(l[4]=a=>g.dateRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(u,null,{default:t(()=>[e(f,{type:"primary",onClick:F,loading:M.value},{default:t(()=>[e(o,null,{default:t(()=>[e(p($e))]),_:1}),l[28]||(l[28]=n(" 搜索 "))]),_:1,__:[28]},8,["loading"]),e(f,{onClick:X},{default:t(()=>[e(o,null,{default:t(()=>[e(p(Le))]),_:1}),l[29]||(l[29]=n(" 重置 "))]),_:1,__:[29]})]),_:1})]),_:1},8,["model"])]),_:1}),e(q,{class:"table-card"},{default:t(()=>[P.value.length>0?(U(),T("div",al,[d("span",sl,"已选择 "+c(P.value.length)+" 个用户",1),d("div",ol,[e(f,{type:"success",size:"small",onClick:l[5]||(l[5]=a=>H(1))},{default:t(()=>[e(o,null,{default:t(()=>[e(p(Ee))]),_:1}),l[30]||(l[30]=n(" 批量启用 "))]),_:1,__:[30]}),e(f,{type:"warning",size:"small",onClick:l[6]||(l[6]=a=>H(0))},{default:t(()=>[e(o,null,{default:t(()=>[e(p(Ne))]),_:1}),l[31]||(l[31]=n(" 批量禁用 "))]),_:1,__:[31]}),e(f,{type:"primary",size:"small",onClick:l[7]||(l[7]=a=>ee("VIP"))},{default:t(()=>[e(o,null,{default:t(()=>[e(p(Fe))]),_:1}),l[32]||(l[32]=n(" 设为VIP "))]),_:1,__:[32]}),e(f,{type:"danger",size:"small",onClick:le},{default:t(()=>[e(o,null,{default:t(()=>[e(p(J))]),_:1}),l[33]||(l[33]=n(" 批量删除 "))]),_:1,__:[33]})])])):D("",!0),Be((U(),$(he,{data:j.value,onSelectionChange:Z,"row-key":"id",class:"user-table"},{default:t(()=>[e(w,{type:"selection",width:"55"}),e(w,{label:"用户信息","min-width":"200"},{default:t(({row:a})=>[d("div",nl,[e(Ue,{size:40,src:a.avatar},{default:t(()=>[e(o,null,{default:t(()=>[e(p(Ae))]),_:1})]),_:2},1032,["src"]),d("div",dl,[d("div",il,[n(c(a.nickname||a.username)+" ",1),a.isVerified?(U(),$(o,{key:0,class:"verified-icon",color:"#67c23a"},{default:t(()=>[e(p(je))]),_:1})):D("",!0)]),d("div",rl,"@"+c(a.username),1),d("div",ul,c(a.email),1)])])]),_:1}),e(w,{label:"角色",width:"120"},{default:t(({row:a})=>[d("div",ml,[(U(!0),T(He,null,Oe(a.roles,I=>(U(),$(K,{key:I,type:ye(I),size:"small",class:"role-tag"},{default:t(()=>[n(c(we(I)),1)]),_:2},1032,["type"]))),128))])]),_:1}),e(w,{label:"状态",width:"100"},{default:t(({row:a})=>[e(K,{type:be(a.status),size:"small"},{default:t(()=>[n(c(Ve(a.status)),1)]),_:2},1032,["type"])]),_:1}),e(w,{label:"联系方式",width:"140"},{default:t(({row:a})=>[d("div",pl,[a.phone?(U(),T("div",cl,c(a.phone),1)):D("",!0),d("div",fl,c(a.location||"未设置"),1)])]),_:1}),e(w,{label:"数据统计",width:"120"},{default:t(({row:a})=>[d("div",_l,[d("div",null,"食谱: "+c(a.recipeCount||0),1),d("div",null,"粉丝: "+c(a.followersCount||0),1),d("div",null,"关注: "+c(a.followingCount||0),1)])]),_:1}),e(w,{label:"最后活动",width:"160"},{default:t(({row:a})=>[d("div",gl,[d("div",vl," 最后登录: "+c(O(a.lastLoginTime)),1),d("div",bl,"IP: "+c(a.lastLoginIp||"未知"),1)])]),_:1}),e(w,{label:"注册时间",width:"140"},{default:t(({row:a})=>[d("div",Vl,c(O(a.registrationTime)),1)]),_:1}),e(w,{label:"操作",width:"180",fixed:"right"},{default:t(({row:a})=>[d("div",yl,[e(f,{type:"primary",size:"small",onClick:I=>se(a.id)},{default:t(()=>[e(o,null,{default:t(()=>[e(p(qe))]),_:1}),l[34]||(l[34]=n(" 详情 "))]),_:2,__:[34]},1032,["onClick"]),e(Ie,{onCommand:ae,trigger:"click"},{dropdown:t(()=>[e(Ce,null,{default:t(()=>[e(C,{command:{action:"edit",user:a}},{default:t(()=>[e(o,null,{default:t(()=>[e(p(Ge))]),_:1}),l[36]||(l[36]=n(" 编辑用户 "))]),_:2,__:[36]},1032,["command"]),e(C,{command:{action:"resetPassword",user:a}},{default:t(()=>[e(o,null,{default:t(()=>[e(p(Je))]),_:1}),l[37]||(l[37]=n(" 重置密码 "))]),_:2,__:[37]},1032,["command"]),e(C,{command:{action:"toggleStatus",user:a},divided:""},{default:t(()=>[n(c(a.status===1?"禁用账户":"启用账户"),1)]),_:2},1032,["command"]),e(C,{command:{action:"toggleRole",user:a}},{default:t(()=>[n(c(a.roles.includes("VIP")?"取消VIP":"设为VIP"),1)]),_:2},1032,["command"]),e(C,{command:{action:"sendMessage",user:a}},{default:t(()=>[e(o,null,{default:t(()=>[e(p(Qe))]),_:1}),l[38]||(l[38]=n(" 发送消息 "))]),_:2,__:[38]},1032,["command"]),e(C,{command:{action:"loginHistory",user:a}},{default:t(()=>[e(o,null,{default:t(()=>[e(p(We))]),_:1}),l[39]||(l[39]=n(" 登录历史 "))]),_:2,__:[39]},1032,["command"]),e(C,{command:{action:"delete",user:a},disabled:a.roles.includes("ADMIN"),divided:""},{default:t(()=>[e(o,null,{default:t(()=>[e(p(J))]),_:1}),l[40]||(l[40]=n(" 删除用户 "))]),_:2,__:[40]},1032,["command","disabled"])]),_:2},1024)]),default:t(()=>[e(f,{type:"text",size:"small"},{default:t(()=>[l[35]||(l[35]=n(" 更多")),e(o,{class:"el-icon--right"},{default:t(()=>[e(p(Ke))]),_:1})]),_:1,__:[35]})]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Se,M.value]]),d("div",wl,[e(xe,{"current-page":b.page,"onUpdate:currentPage":l[8]||(l[8]=a=>b.page=a),"page-size":b.pageSize,"onUpdate:pageSize":l[9]||(l[9]=a=>b.pageSize=a),total:b.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ve,onCurrentChange:ge},null,8,["current-page","page-size","total"])])]),_:1}),e(G,{modelValue:x.value,"onUpdate:modelValue":l[19]||(l[19]=a=>x.value=a),title:i.id?"编辑用户":"添加用户",width:"600px"},{footer:t(()=>[d("span",kl,[e(f,{onClick:l[18]||(l[18]=a=>x.value=!1)},{default:t(()=>l[44]||(l[44]=[n("取消")])),_:1,__:[44]}),e(f,{type:"primary",onClick:de,loading:L.value},{default:t(()=>l[45]||(l[45]=[n(" 保存 ")])),_:1,__:[45]},8,["loading"])])]),default:t(()=>[e(Y,{ref_key:"editFormRef",ref:E,model:i,rules:W,"label-width":"100px"},{default:t(()=>[e(u,{label:"用户名",prop:"username"},{default:t(()=>[e(V,{modelValue:i.username,"onUpdate:modelValue":l[10]||(l[10]=a=>i.username=a),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),e(u,{label:"昵称",prop:"nickname"},{default:t(()=>[e(V,{modelValue:i.nickname,"onUpdate:modelValue":l[11]||(l[11]=a=>i.nickname=a),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),e(u,{label:"邮箱",prop:"email"},{default:t(()=>[e(V,{modelValue:i.email,"onUpdate:modelValue":l[12]||(l[12]=a=>i.email=a),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),e(u,{label:"手机号",prop:"phone"},{default:t(()=>[e(V,{modelValue:i.phone,"onUpdate:modelValue":l[13]||(l[13]=a=>i.phone=a),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),i.id?D("",!0):(U(),$(u,{key:0,label:"密码",prop:"password"},{default:t(()=>[e(V,{modelValue:i.password,"onUpdate:modelValue":l[14]||(l[14]=a=>i.password=a),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1})),e(u,{label:"角色",prop:"roles"},{default:t(()=>[e(R,{modelValue:i.roles,"onUpdate:modelValue":l[15]||(l[15]=a=>i.roles=a),multiple:"",placeholder:"选择角色"},{default:t(()=>[e(_,{label:"普通用户",value:"USER"}),e(_,{label:"VIP用户",value:"VIP"}),e(_,{label:"管理员",value:"ADMIN"})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"状态",prop:"status"},{default:t(()=>[e(Pe,{modelValue:i.status,"onUpdate:modelValue":l[16]||(l[16]=a=>i.status=a)},{default:t(()=>[e(A,{label:1},{default:t(()=>l[41]||(l[41]=[n("正常")])),_:1,__:[41]}),e(A,{label:0},{default:t(()=>l[42]||(l[42]=[n("禁用")])),_:1,__:[42]}),e(A,{label:2},{default:t(()=>l[43]||(l[43]=[n("待激活")])),_:1,__:[43]})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"个人简介",prop:"bio"},{default:t(()=>[e(V,{modelValue:i.bio,"onUpdate:modelValue":l[17]||(l[17]=a=>i.bio=a),type:"textarea",rows:3,placeholder:"请输入个人简介"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(G,{modelValue:S.value,"onUpdate:modelValue":l[23]||(l[23]=a=>S.value=a),title:"发送消息",width:"500px"},{footer:t(()=>[d("span",Ul,[e(f,{onClick:l[22]||(l[22]=a=>S.value=!1)},{default:t(()=>l[46]||(l[46]=[n("取消")])),_:1,__:[46]}),e(f,{type:"primary",onClick:pe,loading:B.value},{default:t(()=>l[47]||(l[47]=[n(" 发送 ")])),_:1,__:[47]},8,["loading"])])]),default:t(()=>[e(Y,{model:y,"label-width":"80px"},{default:t(()=>[e(u,{label:"收件人"},{default:t(()=>{var a,I;return[e(V,{value:((a=N.value)==null?void 0:a.nickname)||((I=N.value)==null?void 0:I.username),readonly:""},null,8,["value"])]}),_:1}),e(u,{label:"消息标题"},{default:t(()=>[e(V,{modelValue:y.title,"onUpdate:modelValue":l[20]||(l[20]=a=>y.title=a),placeholder:"请输入消息标题"},null,8,["modelValue"])]),_:1}),e(u,{label:"消息内容"},{default:t(()=>[e(V,{modelValue:y.content,"onUpdate:modelValue":l[21]||(l[21]=a=>y.content=a),type:"textarea",rows:6,placeholder:"请输入消息内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Pl=Ze(Cl,[["__scopeId","data-v-11f1a237"]]);export{Pl as default};
