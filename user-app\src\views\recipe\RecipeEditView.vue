<template>
  <div class="recipe-edit-view">
    <div class="page-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>

      <!-- 编辑表单 -->
      <div v-else-if="recipe" class="edit-card card">
        <div class="card-header">
          <h2 class="page-title">
            <el-icon><Edit /></el-icon>
            编辑食谱
          </h2>
          <p class="page-subtitle">修改您的食谱信息</p>
          
          <div class="breadcrumb">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item @click="router.push('/')">首页</el-breadcrumb-item>
              <el-breadcrumb-item @click="router.push('/my-recipes')">我的食谱</el-breadcrumb-item>
              <el-breadcrumb-item>编辑食谱</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
        </div>

        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          size="large"
          class="edit-form"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            
            <el-form-item label="食谱标题" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="请输入食谱标题"
                maxlength="100"
                show-word-limit
                clearable
              />
            </el-form-item>

            <el-form-item label="食谱描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                placeholder="请描述您的食谱..."
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="封面图片">
              <div class="cover-upload">
                <el-upload
                  class="cover-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="beforeCoverUpload"
                  :http-request="handleCoverUpload"
                >
                  <img v-if="formData.coverImage" :src="getFileUrl(formData.coverImage)" class="cover-image" />
                  <el-icon v-else class="cover-uploader-icon"><Camera /></el-icon>
                </el-upload>
                <div class="upload-tips">
                  <p>点击上传封面图片</p>
                  <p>支持 JPG、PNG 格式，建议尺寸 400x300px</p>
                </div>
              </div>
            </el-form-item>
          </div>

          <!-- 制作信息 -->
          <div class="form-section">
            <h3 class="section-title">制作信息</h3>
            
            <el-row :gutter="0" justify="center">
              <el-col :span="8" class="form-col">
                <el-form-item label="准备时间" prop="prepTime">
                  <el-input-number
                    v-model="formData.prepTime"
                    :min="0"
                    :max="1440"
                    controls-position="right"
                    class="time-input"
                  />
                  <span class="input-suffix">分钟</span>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="form-col">
                <el-form-item label="烹饪时间" prop="cookTime">
                  <el-input-number
                    v-model="formData.cookTime"
                    :min="0"
                    :max="1440"
                    controls-position="right"
                    class="time-input"
                  />
                  <span class="input-suffix">分钟</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0" justify="center">
              <el-col :span="8" class="form-col">
                <el-form-item label="难度等级" prop="difficulty">
                  <el-select v-model="formData.difficulty" placeholder="选择难度">
                    <el-option label="简单" :value="1" />
                    <el-option label="中等" :value="2" />
                    <el-option label="困难" :value="3" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="form-col">
                <el-form-item label="份量" prop="servings">
                  <el-input-number
                    v-model="formData.servings"
                    :min="1"
                    :max="20"
                    controls-position="right"
                    class="time-input"
                  />
                  <span class="input-suffix">人份</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="分类" prop="categoryId">
              <el-select v-model="formData.categoryId" placeholder="选择食谱分类">
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>
          </div>

          <!-- 食材列表 -->
          <div class="form-section">
            <h3 class="section-title">
              食材列表
              <el-button type="primary" size="small" @click="addIngredient">
                <el-icon><Plus /></el-icon>
                添加食材
              </el-button>
            </h3>
            
            <div class="ingredients-list">
              <div
                v-for="(ingredient, index) in formData.ingredients"
                :key="index"
                class="ingredient-item"
              >
                <el-row :gutter="15">
                  <el-col :span="8">
                    <el-input
                      v-model="ingredient.name"
                      placeholder="食材名称"
                      maxlength="50"
                    />
                  </el-col>
                  <el-col :span="6">
                    <el-input
                      v-model="ingredient.amount"
                      placeholder="用量"
                      maxlength="20"
                    />
                  </el-col>
                  <el-col :span="4">
                    <el-input
                      v-model="ingredient.unit"
                      placeholder="单位"
                      maxlength="10"
                    />
                  </el-col>
                  <el-col :span="4">
                    <el-input
                      v-model="ingredient.notes"
                      placeholder="备注"
                      maxlength="30"
                    />
                  </el-col>
                  <el-col :span="2">
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeIngredient(index)"
                      :disabled="formData.ingredients.length === 1"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>

          <!-- 制作步骤 -->
          <div class="form-section">
            <h3 class="section-title">
              制作步骤
              <el-button type="primary" size="small" @click="addInstruction">
                <el-icon><Plus /></el-icon>
                添加步骤
              </el-button>
            </h3>
            
            <div class="instructions-list">
              <div
                v-for="(instruction, index) in formData.instructions"
                :key="index"
                class="instruction-item"
              >
                <div class="instruction-header">
                  <span class="step-number">步骤 {{ index + 1 }}</span>
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeInstruction(index)"
                    :disabled="formData.instructions.length === 1"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                
                <el-input
                  v-model="instruction.description"
                  type="textarea"
                  placeholder="请描述这一步的操作..."
                  :rows="3"
                  maxlength="500"
                  show-word-limit
                />
                
                <div class="instruction-meta">
                  <el-input-number
                    v-model="instruction.duration"
                    :min="0"
                    :max="120"
                    controls-position="right"
                    placeholder="耗时"
                  />
                  <span class="input-suffix">分钟（可选）</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 小贴士和标签 -->
          <div class="form-section">
            <h3 class="section-title">补充信息</h3>
            
            <el-form-item label="小贴士">
              <el-input
                v-model="formData.tips"
                type="textarea"
                placeholder="分享一些制作小技巧和注意事项..."
                :rows="3"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="标签">
              <el-tag
                v-for="tag in formData.tags"
                :key="tag"
                class="tag-item"
                closable
                @close="removeTag(tag)"
              >
                {{ tag }}
              </el-tag>
              <el-input
                v-if="tagInputVisible"
                ref="tagInputRef"
                v-model="tagInputValue"
                class="tag-input"
                size="small"
                @keyup.enter="addTag"
                @blur="addTag"
              />
              <el-button v-else class="tag-add-button" size="small" @click="showTagInput">
                <el-icon><Plus /></el-icon>
                添加标签
              </el-button>
            </el-form-item>

            <el-form-item label="公开设置">
              <el-radio-group v-model="formData.isPublic">
                <el-radio :label="1">公开 - 所有人都可以看到</el-radio>
                <el-radio :label="0">私有 - 只有自己可以看到</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="发布状态">
              <el-radio-group v-model="formData.status">
                <el-radio :label="0">保存为草稿</el-radio>
                <el-radio :label="1">立即发布</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button size="large" @click="goBack">
              取消
            </el-button>
            <el-button
              size="large"
              @click="saveDraft"
              :loading="draftLoading"
            >
              {{ draftLoading ? '保存中...' : '保存草稿' }}
            </el-button>
            <el-button
              type="primary"
              size="large"
              :loading="submitLoading"
              @click="updateRecipe"
            >
              {{ submitLoading ? '更新中...' : '更新食谱' }}
            </el-button>
          </div>
        </el-form>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-container">
        <el-empty description="食谱不存在或无权限编辑">
          <el-button type="primary" @click="router.push('/my-recipes')">
            返回我的食谱
          </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  Edit,
  Plus,
  Camera,
  Delete
} from '@element-plus/icons-vue'
import type { Recipe, Category, Ingredient, Instruction, RecipeCreateRequest } from '@/types/recipe'
import { recipesApi } from '@/api/recipes'
import { getFileUrl } from '@/utils/file'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const submitLoading = ref(false)
const draftLoading = ref(false)
const formRef = ref<FormInstance>()
const categories = ref<Category[]>([])
const recipe = ref<Recipe | null>(null)
const tagInputVisible = ref(false)
const tagInputValue = ref('')
const tagInputRef = ref()

// 计算属性
const recipeId = ref(Number(route.params.id))

// 表单数据
const formData = reactive<RecipeCreateRequest & { status: number }>({
  title: '',
  description: '',
  coverImage: '',
  prepTime: 30,
  cookTime: 30,
  difficulty: 1,
  servings: 2,
  categoryId: 1,
  ingredients: [
    { name: '', amount: '', unit: '', notes: '' }
  ],
  instructions: [
    { step: 1, description: '', duration: 0 }
  ],
  tips: '',
  tags: [],
  isPublic: 1,
  status: 1
})

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入食谱标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在2-100个字符', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择食谱分类', trigger: 'change' }
  ],
  prepTime: [
    { required: true, message: '请输入准备时间', trigger: 'blur' }
  ],
  cookTime: [
    { required: true, message: '请输入烹饪时间', trigger: 'blur' }
  ],
  difficulty: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ],
  servings: [
    { required: true, message: '请输入份量', trigger: 'blur' }
  ]
}

// 获取食谱详情
const fetchRecipeDetail = async () => {
  loading.value = true
  try {
    // 调用API获取食谱详情
    const { data } = await recipesApi.getRecipeDetail(recipeId.value)
    
    if (data) {
      recipe.value = data
      fillFormData(data)
    } else {
      ElMessage.error('食谱不存在')
    }

  } catch (error) {
    console.error('获取食谱详情失败:', error)
    ElMessage.error('获取食谱详情失败')
  } finally {
    loading.value = false
  }
}

// 填充表单数据
const fillFormData = (recipeData: Recipe) => {
  formData.title = recipeData.title
  formData.description = recipeData.description || ''
  formData.coverImage = recipeData.coverImage || ''
  formData.prepTime = recipeData.prepTime || 30
  formData.cookTime = recipeData.cookTime || 30
  formData.difficulty = recipeData.difficulty || 1
  formData.servings = recipeData.servings || 2
  formData.categoryId = recipeData.categoryId || 1
  formData.tips = recipeData.tips || ''
  formData.isPublic = recipeData.isPublic || 1
  formData.status = recipeData.status || 1
  
  // 处理标签
  if (recipeData.tags) {
    formData.tags = recipeData.tags.split(',').filter(tag => tag.trim())
  }
  
  // 模拟食材数据（实际应该从API获取）
  formData.ingredients = [
    { name: '五花肉', amount: '500', unit: 'g', notes: '选肥瘦相间的' },
    { name: '冰糖', amount: '30', unit: 'g' },
    { name: '生抽', amount: '2', unit: '汤匙' },
    { name: '老抽', amount: '1', unit: '汤匙' },
    { name: '料酒', amount: '2', unit: '汤匙' },
    { name: '葱段', amount: '3', unit: '根' },
    { name: '姜片', amount: '5', unit: '片' },
    { name: '八角', amount: '2', unit: '个' }
  ]
  
  // 模拟制作步骤
  formData.instructions = [
    {
      step: 1,
      description: '将五花肉洗净，切成3cm见方的块状，用开水焯烫去血水，捞出备用。',
      duration: 10
    },
    {
      step: 2,
      description: '锅内放少量油，小火加热，放入冰糖炒至焦糖色。',
      duration: 5
    },
    {
      step: 3,
      description: '下入肉块翻炒，让每块肉都裹上糖色。',
      duration: 8
    },
    {
      step: 4,
      description: '加入生抽、老抽、料酒、葱段、姜片、八角，翻炒均匀。',
      duration: 3
    },
    {
      step: 5,
      description: '加入足够的热水，大火烧开后转小火炖煮1小时。',
      duration: 60
    },
    {
      step: 6,
      description: '大火收汁，让汤汁浓稠包裹在肉块上即可。',
      duration: 10
    }
  ]
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    categories.value = [
      { id: 1, name: '中式料理', isEnabled: 1 },
      { id: 2, name: '西式料理', isEnabled: 1 },
      { id: 3, name: '日式料理', isEnabled: 1 },
      { id: 4, name: '韩式料理', isEnabled: 1 },
      { id: 5, name: '甜品烘焙', isEnabled: 1 },
      { id: 6, name: '汤品饮品', isEnabled: 1 }
    ]
  } catch (error) {
    console.error('获取分类失败:', error)
  }
}

// 封面图片上传前验证
const beforeCoverUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 处理封面图片上传
const handleCoverUpload = (options: any) => {
  const file = options.file
  
  const reader = new FileReader()
  reader.onload = (e) => {
    formData.coverImage = e.target?.result as string
  }
  reader.readAsDataURL(file)
  
  return Promise.resolve()
}

// 添加食材
const addIngredient = () => {
  formData.ingredients.push({
    name: '',
    amount: '',
    unit: '',
    notes: ''
  })
}

// 删除食材
const removeIngredient = (index: number) => {
  if (formData.ingredients.length > 1) {
    formData.ingredients.splice(index, 1)
  }
}

// 添加制作步骤
const addInstruction = () => {
  formData.instructions.push({
    step: formData.instructions.length + 1,
    description: '',
    duration: 0
  })
}

// 删除制作步骤
const removeInstruction = (index: number) => {
  if (formData.instructions.length > 1) {
    formData.instructions.splice(index, 1)
    // 重新编号
    formData.instructions.forEach((instruction, i) => {
      instruction.step = i + 1
    })
  }
}

// 显示标签输入框
const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

// 添加标签
const addTag = () => {
  const tag = tagInputValue.value.trim()
  if (tag && !formData.tags?.includes(tag)) {
    if (!formData.tags) formData.tags = []
    formData.tags.push(tag)
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

// 删除标签
const removeTag = (tag: string) => {
  if (formData.tags) {
    const index = formData.tags.indexOf(tag)
    if (index > -1) {
      formData.tags.splice(index, 1)
    }
  }
}

// 保存草稿
const saveDraft = async () => {
  draftLoading.value = true
  try {
    // 调用保存草稿的API
    const draftData = { ...formData, status: 0 }
    await recipesApi.saveDraft(draftData)
    ElMessage.success('草稿保存成功')
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败')
  } finally {
    draftLoading.value = false
  }
}

// 更新食谱
const updateRecipe = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 验证食材
    const validIngredients = formData.ingredients.filter(ing => ing.name && ing.amount)
    if (validIngredients.length === 0) {
      ElMessage.error('请至少添加一个食材')
      return
    }

    // 验证步骤
    const validInstructions = formData.instructions.filter(inst => inst.description)
    if (validInstructions.length === 0) {
      ElMessage.error('请至少添加一个制作步骤')
      return
    }

    submitLoading.value = true
    
    // 准备提交数据
    const updateData = {
      ...formData,
      ingredients: validIngredients,
      instructions: validInstructions
    }
    
    // 调用更新食谱的API
    await recipesApi.updateRecipe(recipeId.value, updateData)
    
    ElMessage.success('食谱更新成功')
    router.push('/my-recipes')
    
  } catch (error: any) {
    console.error('更新失败:', error)
    if (error?.message) {
      ElMessage.error(error.message)
    }
  } finally {
    submitLoading.value = false
  }
}

// 返回
const goBack = () => {
  router.back()
}

// 组件挂载
onMounted(() => {
  fetchCategories()
  fetchRecipeDetail()
})
</script>

<style scoped>
.recipe-edit-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  padding: 20px 0;
}

.loading-container,
.error-container {
  padding: 60px 20px;
  text-align: center;
}

.edit-card {
  max-width: 900px;
  margin: 0 auto;
  padding: 30px;
}

.card-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 20px;
}

.breadcrumb {
  margin-top: 15px;
}

.breadcrumb :deep(.el-breadcrumb__item) {
  cursor: pointer;
}

.edit-form {
  margin-top: 20px;
}

.form-section {
  margin-bottom: 40px;
  padding: 20px;
  background: var(--bg-light);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 封面上传 */
.cover-upload {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.cover-uploader {
  display: inline-block;
}

:deep(.cover-uploader .el-upload) {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  width: 200px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.cover-uploader .el-upload:hover) {
  border-color: var(--primary-color);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-uploader-icon {
  font-size: 48px;
  color: var(--text-light);
}

.upload-tips {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* 时间输入 */
.time-input {
  width: 100%;
}

.input-suffix {
  margin-left: 8px;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* 食材列表 */
.ingredients-list {
  margin-top: 15px;
}

.ingredient-item {
  margin-bottom: 15px;
  padding: 15px;
  background: white;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

/* 制作步骤 */
.instructions-list {
  margin-top: 15px;
}

.instruction-item {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.instruction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.step-number {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 1.1rem;
}

.instruction-meta {
  margin-top: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 标签 */
.tag-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.tag-input {
  width: 120px;
  margin-right: 10px;
}

.tag-add-button {
  border-style: dashed;
  height: 32px;
}

/* 操作按钮 */
.form-actions {
  text-align: center;
  padding: 30px 0;
  border-top: 1px solid var(--border-color);
  margin-top: 30px;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cover-upload {
    flex-direction: column;
  }
  
  .ingredient-item .el-col {
    margin-bottom: 10px;
  }
  
  .form-actions .el-button {
    display: block;
    margin: 10px auto;
    width: 200px;
  }
}

/* 调整输入框宽度 */
:deep(.el-input-number),
:deep(.el-select) {
  width: 180px !important;
}

:deep(.el-input-number .el-input__inner) {
  width: 100px !important;
  text-align: left;
}

:deep(.input-suffix) {
  margin-left: 8px;
  color: #606266;
}

/* 确保表单项有足够的空间 */
:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}

.form-col {
  margin-left: 24px;
  margin-right: 24px;
}
</style> 