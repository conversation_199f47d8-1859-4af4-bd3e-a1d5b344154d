import{d as _e,r as p,a as A,aq as pe,ar as ve,as as fe,at as ge,o as be,ah as ye,p as w,c as f,e as o,f as e,w as a,j as r,n as d,h as y,G as he,N as Ce,I as E,g as ke,H as Ie,S as we,T as xe,t as n,J as h,P as R,$ as F,D as Ne,a1 as H,k as c,_ as Ve,Z as ze,C as $e}from"./index-BDkHONMN.js";import{_ as De}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Te={class:"system-logs-page"},Se={class:"page-header"},qe={class:"header-actions"},Ue={class:"stat-content"},Le={class:"stat-info"},Me={class:"stat-value"},Re={class:"stat-label"},je={class:"table-header"},Pe={class:"table-actions"},Be={class:"log-time"},Oe={class:"log-message"},Ye={class:"message-content"},Ae={key:0,class:"message-meta"},Ee={key:0,class:"meta-item"},Fe={key:1,class:"meta-item"},He={key:2,class:"meta-item"},Je={class:"log-source"},Ge={class:"method-name"},Ke={class:"pagination-wrapper"},Qe={key:0,class:"log-detail"},We={class:"detail-header"},Ze={class:"detail-meta"},Xe={class:"time"},el={class:"detail-section"},ll={class:"message-box"},al={key:0,class:"detail-section"},tl={class:"stack-trace"},sl={key:1,class:"detail-section"},ol={class:"context-info"},nl={class:"dialog-footer"},dl=_e({__name:"SystemLogs",setup(il){const x=p(!1),$=p(!1),D=p(!1),V=p(!1),T=p(!1),k=p(null),S=p(50),m=A({level:"",module:"",keyword:"",dateRange:[]}),g=A({page:1,size:50,total:0}),i=p(null),J=p([{level:"info",label:"信息日志",count:12485,icon:pe},{level:"warn",label:"警告日志",count:345,icon:ve},{level:"error",label:"错误日志",count:89,icon:fe},{level:"debug",label:"调试日志",count:5620,icon:ge}]),G=p([{id:"1",timestamp:"2024-01-20 14:30:25.123",level:"info",module:"auth",message:"用户登录成功",className:"AuthController",methodName:"login",ip:"*************",userId:"1001",requestId:"req_20240120_001",thread:"http-nio-8080-exec-1",serverName:"recipe-server-01"},{id:"2",timestamp:"2024-01-20 14:28:15.456",level:"warn",module:"upload",message:"文件上传超时，文件大小: 15MB",className:"FileUploadService",methodName:"uploadFile",ip:"*************",userId:"1002",requestId:"req_20240120_002",thread:"http-nio-8080-exec-2",serverName:"recipe-server-01"},{id:"3",timestamp:"2024-01-20 14:25:42.789",level:"error",module:"database",message:"数据库连接超时",className:"DatabaseConnectionPool",methodName:"getConnection",requestId:"req_20240120_003",thread:"connection-pool-1",serverName:"recipe-server-01",stackTrace:`java.sql.SQLException: Connection timeout
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)`,context:{connectionUrl:"*****************************************",timeout:3e4,activeConnections:20,maxConnections:20}}]);be(()=>{C()}),ye(()=>{k.value&&clearInterval(k.value)});const C=async()=>{x.value=!0;try{await new Promise(s=>setTimeout(s,1e3)),g.total=1500}catch{w.error("加载日志失败")}finally{x.value=!1}},q=()=>{g.page=1,C()},K=()=>{Object.assign(m,{level:"",module:"",keyword:"",dateRange:[]}),q()},j=()=>{g.size=S.value,C()},Q=()=>{C()},W=()=>{C()},P=s=>{i.value=s,V.value=!0},B=async s=>{if(!s)return;const l=`
时间: ${s.timestamp}
级别: ${s.level.toUpperCase()}
模块: ${U(s.module)}
消息: ${s.message}
类名: ${s.className}
方法: ${s.methodName}
${s.ip?`IP: ${s.ip}`:""}
${s.userId?`用户ID: ${s.userId}`:""}
${s.requestId?`请求ID: ${s.requestId}`:""}
${s.stackTrace?`
异常堆栈:
${s.stackTrace}`:""}
${s.context?`
上下文:
${JSON.stringify(s.context,null,2)}`:""}
  `.trim();try{await navigator.clipboard.writeText(l),w.success("日志内容已复制到剪贴板")}catch{w.error("复制失败，请手动复制")}},Z=async()=>{$.value=!0;try{await new Promise(s=>setTimeout(s,2e3)),w.success("日志导出成功")}catch{w.error("导出失败")}finally{$.value=!1}},X=async()=>{try{await $e.confirm("确定要清理历史日志吗？建议清理30天前的日志数据。","清理日志",{type:"warning",confirmButtonText:"确认清理",cancelButtonText:"取消"}),D.value=!0,await new Promise(s=>setTimeout(s,2e3)),w.success("日志清理成功"),C()}catch{}finally{D.value=!1}},ee=()=>{T.value?k.value=window.setInterval(()=>{C()},5e3):k.value&&(clearInterval(k.value),k.value=null)},le=s=>s.toLocaleString(),O=s=>new Date(s).toLocaleString("zh-CN"),Y=s=>({debug:"info",info:"success",warn:"warning",error:"danger",fatal:"danger"})[s]||"",U=s=>({user:"用户管理",recipe:"食谱管理",auth:"认证授权",upload:"文件上传",database:"数据库",cache:"缓存",api:"API接口",task:"定时任务"})[s]||s;return(s,l)=>{const _=r("el-icon"),v=r("el-button"),u=r("el-option"),L=r("el-select"),N=r("el-form-item"),ae=r("el-input"),te=r("el-date-picker"),se=r("el-form"),M=r("el-card"),oe=r("el-col"),ne=r("el-row"),de=r("el-switch"),I=r("el-table-column"),z=r("el-tag"),ie=r("el-table"),ue=r("el-pagination"),b=r("el-descriptions-item"),re=r("el-descriptions"),ce=r("el-dialog");return c(),f("div",Te,[o("div",Se,[l[14]||(l[14]=o("h2",null,"系统日志",-1)),l[15]||(l[15]=o("p",null,"查看和管理系统运行日志",-1)),o("div",qe,[e(v,{onClick:Z,loading:$.value},{default:a(()=>[e(_,null,{default:a(()=>[e(y(he))]),_:1}),l[11]||(l[11]=d(" 导出日志 "))]),_:1,__:[11]},8,["loading"]),e(v,{onClick:X,loading:D.value},{default:a(()=>[e(_,null,{default:a(()=>[e(y(Ce))]),_:1}),l[12]||(l[12]=d(" 清理日志 "))]),_:1,__:[12]},8,["loading"]),e(v,{type:"primary",onClick:W,loading:x.value},{default:a(()=>[e(_,null,{default:a(()=>[e(y(E))]),_:1}),l[13]||(l[13]=d(" 刷新 "))]),_:1,__:[13]},8,["loading"])])]),e(M,{class:"search-card"},{default:a(()=>[e(se,{model:m,inline:!0,class:"search-form"},{default:a(()=>[e(N,{label:"日志级别"},{default:a(()=>[e(L,{modelValue:m.level,"onUpdate:modelValue":l[0]||(l[0]=t=>m.level=t),placeholder:"选择级别",clearable:""},{default:a(()=>[e(u,{label:"所有级别",value:""}),e(u,{label:"调试 (DEBUG)",value:"debug"}),e(u,{label:"信息 (INFO)",value:"info"}),e(u,{label:"警告 (WARN)",value:"warn"}),e(u,{label:"错误 (ERROR)",value:"error"}),e(u,{label:"致命 (FATAL)",value:"fatal"})]),_:1},8,["modelValue"])]),_:1}),e(N,{label:"模块"},{default:a(()=>[e(L,{modelValue:m.module,"onUpdate:modelValue":l[1]||(l[1]=t=>m.module=t),placeholder:"选择模块",clearable:""},{default:a(()=>[e(u,{label:"所有模块",value:""}),e(u,{label:"用户管理",value:"user"}),e(u,{label:"食谱管理",value:"recipe"}),e(u,{label:"认证授权",value:"auth"}),e(u,{label:"文件上传",value:"upload"}),e(u,{label:"数据库",value:"database"}),e(u,{label:"缓存",value:"cache"}),e(u,{label:"API接口",value:"api"}),e(u,{label:"定时任务",value:"task"})]),_:1},8,["modelValue"])]),_:1}),e(N,{label:"关键词"},{default:a(()=>[e(ae,{modelValue:m.keyword,"onUpdate:modelValue":l[2]||(l[2]=t=>m.keyword=t),placeholder:"搜索日志内容",clearable:"",onKeyup:ke(q,["enter"])},null,8,["modelValue"])]),_:1}),e(N,{label:"时间范围"},{default:a(()=>[e(te,{modelValue:m.dateRange,"onUpdate:modelValue":l[3]||(l[3]=t=>m.dateRange=t),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),e(N,null,{default:a(()=>[e(v,{type:"primary",onClick:q,loading:x.value},{default:a(()=>[e(_,null,{default:a(()=>[e(y(Ie))]),_:1}),l[16]||(l[16]=d(" 搜索 "))]),_:1,__:[16]},8,["loading"]),e(v,{onClick:K},{default:a(()=>[e(_,null,{default:a(()=>[e(y(E))]),_:1}),l[17]||(l[17]=d(" 重置 "))]),_:1,__:[17]})]),_:1})]),_:1},8,["model"])]),_:1}),e(ne,{gutter:20,class:"stats-section"},{default:a(()=>[(c(!0),f(we,null,xe(J.value,t=>(c(),R(oe,{xs:24,sm:12,lg:6,key:t.level},{default:a(()=>[e(M,{class:"stat-card",shadow:"hover"},{default:a(()=>[o("div",Ue,[o("div",{class:Ve(["stat-icon",`icon-${t.level}`])},[e(_,{size:24},{default:a(()=>[(c(),R(ze(t.icon)))]),_:2},1024)],2),o("div",Le,[o("div",Me,n(le(t.count)),1),o("div",Re,n(t.label),1)])])]),_:2},1024)]),_:2},1024))),128))]),_:1}),e(M,{class:"table-card"},{header:a(()=>[o("div",je,[l[18]||(l[18]=o("span",null,"日志记录",-1)),o("div",Pe,[e(de,{modelValue:T.value,"onUpdate:modelValue":l[4]||(l[4]=t=>T.value=t),"active-text":"自动刷新","inactive-text":"手动刷新",onChange:ee},null,8,["modelValue"]),e(L,{modelValue:S.value,"onUpdate:modelValue":l[5]||(l[5]=t=>S.value=t),size:"small",style:{width:"120px"},onChange:j},{default:a(()=>[e(u,{label:"20条/页",value:20}),e(u,{label:"50条/页",value:50}),e(u,{label:"100条/页",value:100}),e(u,{label:"200条/页",value:200})]),_:1},8,["modelValue"])])])]),default:a(()=>[e(ie,{data:G.value,loading:x.value,"row-key":"id",stripe:"",size:"small",onRowClick:P},{default:a(()=>[e(I,{label:"时间",width:"160"},{default:a(({row:t})=>[o("div",Be,n(O(t.timestamp)),1)]),_:1}),e(I,{label:"级别",width:"80"},{default:a(({row:t})=>[e(z,{type:Y(t.level),size:"small"},{default:a(()=>[d(n(t.level.toUpperCase()),1)]),_:2},1032,["type"])]),_:1}),e(I,{label:"模块",width:"100"},{default:a(({row:t})=>[e(z,{type:"info",size:"small"},{default:a(()=>[d(n(U(t.module)),1)]),_:2},1024)]),_:1}),e(I,{label:"消息","min-width":"300"},{default:a(({row:t})=>[o("div",Oe,[o("div",Ye,n(t.message),1),t.ip||t.userId?(c(),f("div",Ae,[t.ip?(c(),f("span",Ee,"IP: "+n(t.ip),1)):h("",!0),t.userId?(c(),f("span",Fe,"用户ID: "+n(t.userId),1)):h("",!0),t.requestId?(c(),f("span",He,"请求ID: "+n(t.requestId),1)):h("",!0)])):h("",!0)])]),_:1}),e(I,{label:"来源",width:"120"},{default:a(({row:t})=>[o("div",Je,[o("div",null,n(t.className),1),o("div",Ge,n(t.methodName),1)])]),_:1}),e(I,{label:"操作",width:"120"},{default:a(({row:t})=>[e(v,{type:"text",size:"small",onClick:F(me=>P(t),["stop"])},{default:a(()=>[e(_,null,{default:a(()=>[e(y(Ne))]),_:1}),l[19]||(l[19]=d(" 详情 "))]),_:2,__:[19]},1032,["onClick"]),t.level==="error"||t.level==="fatal"?(c(),R(v,{key:0,type:"text",size:"small",onClick:F(me=>B(t),["stop"])},{default:a(()=>[e(_,null,{default:a(()=>[e(y(H))]),_:1}),l[20]||(l[20]=d(" 复制 "))]),_:2,__:[20]},1032,["onClick"])):h("",!0)]),_:1})]),_:1},8,["data","loading"]),o("div",Ke,[e(ue,{"current-page":g.page,"onUpdate:currentPage":l[6]||(l[6]=t=>g.page=t),"page-size":g.size,"onUpdate:pageSize":l[7]||(l[7]=t=>g.size=t),total:g.total,"page-sizes":[20,50,100,200],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:j,onCurrentChange:Q},null,8,["current-page","page-size","total"])])]),_:1}),e(ce,{modelValue:V.value,"onUpdate:modelValue":l[10]||(l[10]=t=>V.value=t),title:"日志详情",width:"800px"},{footer:a(()=>[o("div",nl,[e(v,{onClick:l[8]||(l[8]=t=>V.value=!1)},{default:a(()=>l[24]||(l[24]=[d("关闭")])),_:1,__:[24]}),e(v,{type:"primary",onClick:l[9]||(l[9]=t=>B(i.value))},{default:a(()=>[e(_,null,{default:a(()=>[e(y(H))]),_:1}),l[25]||(l[25]=d(" 复制日志 "))]),_:1,__:[25]})])]),default:a(()=>[i.value?(c(),f("div",Qe,[o("div",We,[o("div",Ze,[e(z,{type:Y(i.value.level),size:"small"},{default:a(()=>[d(n(i.value.level.toUpperCase()),1)]),_:1},8,["type"]),e(z,{type:"info",size:"small"},{default:a(()=>[d(n(U(i.value.module)),1)]),_:1}),o("span",Xe,n(O(i.value.timestamp)),1)])]),e(re,{column:2,border:"",class:"detail-info"},{default:a(()=>[e(b,{label:"日志ID"},{default:a(()=>[d(n(i.value.id),1)]),_:1}),e(b,{label:"请求ID"},{default:a(()=>[d(n(i.value.requestId||"-"),1)]),_:1}),e(b,{label:"用户ID"},{default:a(()=>[d(n(i.value.userId||"-"),1)]),_:1}),e(b,{label:"IP地址"},{default:a(()=>[d(n(i.value.ip||"-"),1)]),_:1}),e(b,{label:"类名"},{default:a(()=>[d(n(i.value.className),1)]),_:1}),e(b,{label:"方法名"},{default:a(()=>[d(n(i.value.methodName),1)]),_:1}),e(b,{label:"线程"},{default:a(()=>[d(n(i.value.thread||"-"),1)]),_:1}),e(b,{label:"服务器"},{default:a(()=>[d(n(i.value.serverName||"-"),1)]),_:1})]),_:1}),o("div",el,[l[21]||(l[21]=o("h4",null,"日志消息",-1)),o("div",ll,n(i.value.message),1)]),i.value.stackTrace?(c(),f("div",al,[l[22]||(l[22]=o("h4",null,"异常堆栈",-1)),o("div",tl,n(i.value.stackTrace),1)])):h("",!0),i.value.context?(c(),f("div",sl,[l[23]||(l[23]=o("h4",null,"上下文信息",-1)),o("pre",ol,n(JSON.stringify(i.value.context,null,2)),1)])):h("",!0)])):h("",!0)]),_:1},8,["modelValue"])])}}}),cl=De(dl,[["__scopeId","data-v-54960c31"]]);export{cl as default};
