import{d as le,r as c,a as L,ao as ae,o as te,p as b,c as oe,e as V,f as e,P as F,J as W,w as l,j as g,n as v,h as M,af as de,G as ue,L as ne,ap as se,N as ie,C,k as q}from"./index-BDkHONMN.js";import{_ as me}from"./_plugin-vue_export-helper-DlAUqK2U.js";const re={class:"system-config-page"},pe={class:"page-header"},fe={class:"header-actions"},Ve={class:"config-section-header"},ge={class:"config-section-header"},_e={class:"header-buttons"},be={class:"config-section-header"},we={class:"header-buttons"},ye={class:"config-section-header"},ve={class:"config-section-header"},xe=le({__name:"SystemConfig",setup(ce){const B=c("app"),h=c(!1),A=c(!1),j=c(!1),N=c(!1),I=c(!1),T=c(!1),s=L({appName:"个人食谱管理系统",appVersion:"1.0.0",domain:"localhost:8080",apiBaseUrl:"/api/v1",defaultLanguage:"zh-CN",timezone:"Asia/Shanghai",description:"一个专业的个人食谱管理和分享平台",maintenanceMode:!1,debugMode:!1,apiLogging:!0}),u=L({type:"mysql",host:"localhost",port:3306,database:"recipe_system",username:"root",password:"",charset:"utf8mb4",poolSize:10,connectionTimeout:30,queryTimeout:60,autoReconnect:!0}),r=L({type:"redis",host:"localhost",port:6379,password:"",database:0,defaultTtl:3600,enabled:!0,compression:!0,statistics:!0}),i=L({type:"local",localPath:"/var/www/uploads",domain:"http://localhost:8080",urlPrefix:"/uploads",maxFileSize:10,allowedTypes:"jpg,jpeg,png,gif,pdf,doc,docx",imageQuality:80,autoCompress:!0,generateThumbnail:!0,watermark:!1}),n=L({jwtSecret:"your-secret-key-here",tokenExpiration:24,passwordMinLength:8,maxLoginAttempts:5,lockoutDuration:30,sessionTimeout:120,enforcePasswordComplexity:!0,twoFactorAuth:!1,ipWhitelist:!1,allowedIPs:""});ae([s,u,r,i,n],()=>{T.value=!0},{deep:!0}),te(()=>{O()});const O=async()=>{try{console.log("加载配置数据..."),T.value=!1}catch{b.error("加载配置失败")}},E=async()=>{h.value=!0;try{await new Promise(_=>setTimeout(_,2e3)),b.success("配置保存成功"),T.value=!1}catch{b.error("保存配置失败")}finally{h.value=!1}},Q=async()=>{A.value=!0;try{await new Promise(_=>setTimeout(_,1e3)),b.success("配置导入成功"),T.value=!0}catch{b.error("导入配置失败")}finally{A.value=!1}},R=async()=>{j.value=!0;try{await new Promise(_=>setTimeout(_,1e3)),b.success("配置导出成功")}catch{b.error("导出配置失败")}finally{j.value=!1}},D=async()=>{N.value=!0;try{await new Promise(_=>setTimeout(_,2e3)),b.success("数据库连接测试成功")}catch{b.error("数据库连接测试失败")}finally{N.value=!1}},J=async()=>{try{await C.confirm("确定要清空所有缓存吗？","确认操作",{type:"warning"}),I.value=!0,await new Promise(_=>setTimeout(_,1e3)),b.success("缓存清空成功")}catch{}finally{I.value=!1}},G=()=>{C.confirm("确定要重置应用配置为默认值吗？","确认重置",{type:"warning"}).then(()=>{Object.assign(s,{appName:"个人食谱管理系统",appVersion:"1.0.0",domain:"localhost:8080",apiBaseUrl:"/api/v1",defaultLanguage:"zh-CN",timezone:"Asia/Shanghai",description:"一个专业的个人食谱管理和分享平台",maintenanceMode:!1,debugMode:!1,apiLogging:!0}),b.success("应用配置已重置")})},Y=()=>{C.confirm("确定要重置数据库配置为默认值吗？","确认重置",{type:"warning"}).then(()=>{Object.assign(u,{type:"mysql",host:"localhost",port:3306,database:"recipe_system",username:"root",password:"",charset:"utf8mb4",poolSize:10,connectionTimeout:30,queryTimeout:60,autoReconnect:!0}),b.success("数据库配置已重置")})},H=()=>{C.confirm("确定要重置缓存配置为默认值吗？","确认重置",{type:"warning"}).then(()=>{Object.assign(r,{type:"redis",host:"localhost",port:6379,password:"",database:0,defaultTtl:3600,enabled:!0,compression:!0,statistics:!0}),b.success("缓存配置已重置")})},K=()=>{C.confirm("确定要重置存储配置为默认值吗？","确认重置",{type:"warning"}).then(()=>{Object.assign(i,{type:"local",localPath:"/var/www/uploads",domain:"http://localhost:8080",urlPrefix:"/uploads",maxFileSize:10,allowedTypes:"jpg,jpeg,png,gif,pdf,doc,docx",imageQuality:80,autoCompress:!0,generateThumbnail:!0,watermark:!1}),b.success("存储配置已重置")})},X=()=>{C.confirm("确定要重置安全配置为默认值吗？","确认重置",{type:"warning"}).then(()=>{Object.assign(n,{jwtSecret:"your-secret-key-here",tokenExpiration:24,passwordMinLength:8,maxLoginAttempts:5,lockoutDuration:30,sessionTimeout:120,enforcePasswordComplexity:!0,twoFactorAuth:!1,ipWhitelist:!1,allowedIPs:""}),b.success("安全配置已重置")})};return(_,a)=>{const k=g("el-icon"),x=g("el-button"),f=g("el-input"),o=g("el-form-item"),d=g("el-col"),m=g("el-row"),p=g("el-option"),U=g("el-select"),w=g("el-switch"),P=g("el-form"),z=g("el-card"),S=g("el-tab-pane"),y=g("el-input-number"),Z=g("el-slider"),$=g("el-tabs"),ee=g("el-alert");return q(),oe("div",re,[V("div",pe,[a[54]||(a[54]=V("h2",null,"系统配置",-1)),a[55]||(a[55]=V("p",null,"管理系统的详细配置参数",-1)),V("div",fe,[e(x,{onClick:Q,loading:A.value},{default:l(()=>[e(k,null,{default:l(()=>[e(M(de))]),_:1}),a[51]||(a[51]=v(" 导入配置 "))]),_:1,__:[51]},8,["loading"]),e(x,{onClick:R,loading:j.value},{default:l(()=>[e(k,null,{default:l(()=>[e(M(ue))]),_:1}),a[52]||(a[52]=v(" 导出配置 "))]),_:1,__:[52]},8,["loading"]),e(x,{type:"primary",onClick:E,loading:h.value},{default:l(()=>[e(k,null,{default:l(()=>[e(M(ne))]),_:1}),a[53]||(a[53]=v(" 保存所有配置 "))]),_:1,__:[53]},8,["loading"])])]),e($,{modelValue:B.value,"onUpdate:modelValue":a[50]||(a[50]=t=>B.value=t),type:"card",class:"config-tabs"},{default:l(()=>[e(S,{label:"应用配置",name:"app"},{default:l(()=>[e(z,{class:"config-card"},{header:l(()=>[V("div",Ve,[a[57]||(a[57]=V("span",null,"基础应用配置",-1)),e(x,{size:"small",onClick:G},{default:l(()=>a[56]||(a[56]=[v("重置默认")])),_:1,__:[56]})])]),default:l(()=>[e(P,{model:s,"label-width":"150px",size:"default"},{default:l(()=>[e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"应用名称"},{default:l(()=>[e(f,{modelValue:s.appName,"onUpdate:modelValue":a[0]||(a[0]=t=>s.appName=t),placeholder:"请输入应用名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"应用版本"},{default:l(()=>[e(f,{modelValue:s.appVersion,"onUpdate:modelValue":a[1]||(a[1]=t=>s.appVersion=t),placeholder:"请输入版本号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"应用域名"},{default:l(()=>[e(f,{modelValue:s.domain,"onUpdate:modelValue":a[2]||(a[2]=t=>s.domain=t),placeholder:"例: www.example.com"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"API基础路径"},{default:l(()=>[e(f,{modelValue:s.apiBaseUrl,"onUpdate:modelValue":a[3]||(a[3]=t=>s.apiBaseUrl=t),placeholder:"例: /api/v1"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"默认语言"},{default:l(()=>[e(U,{modelValue:s.defaultLanguage,"onUpdate:modelValue":a[4]||(a[4]=t=>s.defaultLanguage=t),style:{width:"100%"}},{default:l(()=>[e(p,{label:"简体中文",value:"zh-CN"}),e(p,{label:"English",value:"en-US"}),e(p,{label:"繁體中文",value:"zh-TW"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"时区设置"},{default:l(()=>[e(U,{modelValue:s.timezone,"onUpdate:modelValue":a[5]||(a[5]=t=>s.timezone=t),style:{width:"100%"}},{default:l(()=>[e(p,{label:"UTC+8 (北京时间)",value:"Asia/Shanghai"}),e(p,{label:"UTC+0 (格林威治)",value:"UTC"}),e(p,{label:"UTC-5 (纽约时间)",value:"America/New_York"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(o,{label:"应用描述"},{default:l(()=>[e(f,{modelValue:s.description,"onUpdate:modelValue":a[6]||(a[6]=t=>s.description=t),type:"textarea",rows:3,placeholder:"请输入应用描述"},null,8,["modelValue"])]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:8},{default:l(()=>[e(o,{label:"维护模式"},{default:l(()=>[e(w,{modelValue:s.maintenanceMode,"onUpdate:modelValue":a[7]||(a[7]=t=>s.maintenanceMode=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:l(()=>[e(o,{label:"调试模式"},{default:l(()=>[e(w,{modelValue:s.debugMode,"onUpdate:modelValue":a[8]||(a[8]=t=>s.debugMode=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:l(()=>[e(o,{label:"API日志"},{default:l(()=>[e(w,{modelValue:s.apiLogging,"onUpdate:modelValue":a[9]||(a[9]=t=>s.apiLogging=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),e(S,{label:"数据库配置",name:"database"},{default:l(()=>[e(z,{class:"config-card"},{header:l(()=>[V("div",ge,[a[60]||(a[60]=V("span",null,"数据库连接配置",-1)),V("div",_e,[e(x,{size:"small",onClick:D,loading:N.value},{default:l(()=>[e(k,null,{default:l(()=>[e(M(se))]),_:1}),a[58]||(a[58]=v(" 测试连接 "))]),_:1,__:[58]},8,["loading"]),e(x,{size:"small",onClick:Y},{default:l(()=>a[59]||(a[59]=[v("重置默认")])),_:1,__:[59]})])])]),default:l(()=>[e(P,{model:u,"label-width":"150px",size:"default"},{default:l(()=>[e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"数据库类型"},{default:l(()=>[e(U,{modelValue:u.type,"onUpdate:modelValue":a[10]||(a[10]=t=>u.type=t),style:{width:"100%"}},{default:l(()=>[e(p,{label:"MySQL",value:"mysql"}),e(p,{label:"PostgreSQL",value:"postgresql"}),e(p,{label:"SQL Server",value:"sqlserver"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"连接池大小"},{default:l(()=>[e(y,{modelValue:u.poolSize,"onUpdate:modelValue":a[11]||(a[11]=t=>u.poolSize=t),min:1,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"服务器地址"},{default:l(()=>[e(f,{modelValue:u.host,"onUpdate:modelValue":a[12]||(a[12]=t=>u.host=t),placeholder:"例: localhost"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"端口号"},{default:l(()=>[e(y,{modelValue:u.port,"onUpdate:modelValue":a[13]||(a[13]=t=>u.port=t),min:1,max:65535,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"数据库名"},{default:l(()=>[e(f,{modelValue:u.database,"onUpdate:modelValue":a[14]||(a[14]=t=>u.database=t),placeholder:"数据库名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"字符集"},{default:l(()=>[e(U,{modelValue:u.charset,"onUpdate:modelValue":a[15]||(a[15]=t=>u.charset=t),style:{width:"100%"}},{default:l(()=>[e(p,{label:"utf8mb4",value:"utf8mb4"}),e(p,{label:"utf8",value:"utf8"}),e(p,{label:"latin1",value:"latin1"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"用户名"},{default:l(()=>[e(f,{modelValue:u.username,"onUpdate:modelValue":a[16]||(a[16]=t=>u.username=t),placeholder:"数据库用户名"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"密码"},{default:l(()=>[e(f,{modelValue:u.password,"onUpdate:modelValue":a[17]||(a[17]=t=>u.password=t),type:"password",placeholder:"数据库密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:8},{default:l(()=>[e(o,{label:"连接超时(秒)"},{default:l(()=>[e(y,{modelValue:u.connectionTimeout,"onUpdate:modelValue":a[18]||(a[18]=t=>u.connectionTimeout=t),min:5,max:300,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:l(()=>[e(o,{label:"查询超时(秒)"},{default:l(()=>[e(y,{modelValue:u.queryTimeout,"onUpdate:modelValue":a[19]||(a[19]=t=>u.queryTimeout=t),min:5,max:300,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:l(()=>[e(o,{label:"自动重连"},{default:l(()=>[e(w,{modelValue:u.autoReconnect,"onUpdate:modelValue":a[20]||(a[20]=t=>u.autoReconnect=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),e(S,{label:"缓存配置",name:"cache"},{default:l(()=>[e(z,{class:"config-card"},{header:l(()=>[V("div",be,[a[63]||(a[63]=V("span",null,"缓存系统配置",-1)),V("div",we,[e(x,{size:"small",onClick:J,loading:I.value},{default:l(()=>[e(k,null,{default:l(()=>[e(M(ie))]),_:1}),a[61]||(a[61]=v(" 清空缓存 "))]),_:1,__:[61]},8,["loading"]),e(x,{size:"small",onClick:H},{default:l(()=>a[62]||(a[62]=[v("重置默认")])),_:1,__:[62]})])])]),default:l(()=>[e(P,{model:r,"label-width":"150px",size:"default"},{default:l(()=>[e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"缓存类型"},{default:l(()=>[e(U,{modelValue:r.type,"onUpdate:modelValue":a[21]||(a[21]=t=>r.type=t),style:{width:"100%"}},{default:l(()=>[e(p,{label:"Redis",value:"redis"}),e(p,{label:"Memcached",value:"memcached"}),e(p,{label:"内存缓存",value:"memory"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"默认过期时间(秒)"},{default:l(()=>[e(y,{modelValue:r.defaultTtl,"onUpdate:modelValue":a[22]||(a[22]=t=>r.defaultTtl=t),min:60,max:86400,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"服务器地址"},{default:l(()=>[e(f,{modelValue:r.host,"onUpdate:modelValue":a[23]||(a[23]=t=>r.host=t),placeholder:"例: localhost"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"端口号"},{default:l(()=>[e(y,{modelValue:r.port,"onUpdate:modelValue":a[24]||(a[24]=t=>r.port=t),min:1,max:65535,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"密码"},{default:l(()=>[e(f,{modelValue:r.password,"onUpdate:modelValue":a[25]||(a[25]=t=>r.password=t),type:"password",placeholder:"Redis密码(可选)","show-password":""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"数据库索引"},{default:l(()=>[e(y,{modelValue:r.database,"onUpdate:modelValue":a[26]||(a[26]=t=>r.database=t),min:0,max:15,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:8},{default:l(()=>[e(o,{label:"启用缓存"},{default:l(()=>[e(w,{modelValue:r.enabled,"onUpdate:modelValue":a[27]||(a[27]=t=>r.enabled=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:l(()=>[e(o,{label:"缓存压缩"},{default:l(()=>[e(w,{modelValue:r.compression,"onUpdate:modelValue":a[28]||(a[28]=t=>r.compression=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:l(()=>[e(o,{label:"缓存统计"},{default:l(()=>[e(w,{modelValue:r.statistics,"onUpdate:modelValue":a[29]||(a[29]=t=>r.statistics=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),e(S,{label:"文件存储",name:"storage"},{default:l(()=>[e(z,{class:"config-card"},{header:l(()=>[V("div",ye,[a[65]||(a[65]=V("span",null,"文件存储配置",-1)),e(x,{size:"small",onClick:K},{default:l(()=>a[64]||(a[64]=[v("重置默认")])),_:1,__:[64]})])]),default:l(()=>[e(P,{model:i,"label-width":"150px",size:"default"},{default:l(()=>[e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"存储类型"},{default:l(()=>[e(U,{modelValue:i.type,"onUpdate:modelValue":a[30]||(a[30]=t=>i.type=t),style:{width:"100%"}},{default:l(()=>[e(p,{label:"本地存储",value:"local"}),e(p,{label:"阿里云OSS",value:"aliyun-oss"}),e(p,{label:"腾讯云COS",value:"tencent-cos"}),e(p,{label:"七牛云",value:"qiniu"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"最大文件大小(MB)"},{default:l(()=>[e(y,{modelValue:i.maxFileSize,"onUpdate:modelValue":a[31]||(a[31]=t=>i.maxFileSize=t),min:1,max:1024,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:24},{default:l(()=>[e(o,{label:"本地存储路径"},{default:l(()=>[e(f,{modelValue:i.localPath,"onUpdate:modelValue":a[32]||(a[32]=t=>i.localPath=t),placeholder:"例: /var/www/uploads"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"访问域名"},{default:l(()=>[e(f,{modelValue:i.domain,"onUpdate:modelValue":a[33]||(a[33]=t=>i.domain=t),placeholder:"例: https://cdn.example.com"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"URL前缀"},{default:l(()=>[e(f,{modelValue:i.urlPrefix,"onUpdate:modelValue":a[34]||(a[34]=t=>i.urlPrefix=t),placeholder:"例: /uploads"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"允许的文件类型"},{default:l(()=>[e(f,{modelValue:i.allowedTypes,"onUpdate:modelValue":a[35]||(a[35]=t=>i.allowedTypes=t),placeholder:"例: jpg,png,gif,pdf"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"图片压缩质量"},{default:l(()=>[e(Z,{modelValue:i.imageQuality,"onUpdate:modelValue":a[36]||(a[36]=t=>i.imageQuality=t),min:1,max:100,"show-input":""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:8},{default:l(()=>[e(o,{label:"自动压缩图片"},{default:l(()=>[e(w,{modelValue:i.autoCompress,"onUpdate:modelValue":a[37]||(a[37]=t=>i.autoCompress=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:l(()=>[e(o,{label:"生成缩略图"},{default:l(()=>[e(w,{modelValue:i.generateThumbnail,"onUpdate:modelValue":a[38]||(a[38]=t=>i.generateThumbnail=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:l(()=>[e(o,{label:"水印功能"},{default:l(()=>[e(w,{modelValue:i.watermark,"onUpdate:modelValue":a[39]||(a[39]=t=>i.watermark=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),e(S,{label:"安全配置",name:"security"},{default:l(()=>[e(z,{class:"config-card"},{header:l(()=>[V("div",ve,[a[67]||(a[67]=V("span",null,"安全策略配置",-1)),e(x,{size:"small",onClick:X},{default:l(()=>a[66]||(a[66]=[v("重置默认")])),_:1,__:[66]})])]),default:l(()=>[e(P,{model:n,"label-width":"150px",size:"default"},{default:l(()=>[e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"JWT密钥"},{default:l(()=>[e(f,{modelValue:n.jwtSecret,"onUpdate:modelValue":a[40]||(a[40]=t=>n.jwtSecret=t),type:"password",placeholder:"JWT签名密钥","show-password":""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"Token过期时间(小时)"},{default:l(()=>[e(y,{modelValue:n.tokenExpiration,"onUpdate:modelValue":a[41]||(a[41]=t=>n.tokenExpiration=t),min:1,max:168,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"密码最小长度"},{default:l(()=>[e(y,{modelValue:n.passwordMinLength,"onUpdate:modelValue":a[42]||(a[42]=t=>n.passwordMinLength=t),min:6,max:20,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"登录失败次数限制"},{default:l(()=>[e(y,{modelValue:n.maxLoginAttempts,"onUpdate:modelValue":a[43]||(a[43]=t=>n.maxLoginAttempts=t),min:3,max:10,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(o,{label:"账户锁定时间(分钟)"},{default:l(()=>[e(y,{modelValue:n.lockoutDuration,"onUpdate:modelValue":a[44]||(a[44]=t=>n.lockoutDuration=t),min:5,max:1440,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(o,{label:"会话超时(分钟)"},{default:l(()=>[e(y,{modelValue:n.sessionTimeout,"onUpdate:modelValue":a[45]||(a[45]=t=>n.sessionTimeout=t),min:15,max:480,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{gutter:20},{default:l(()=>[e(d,{span:8},{default:l(()=>[e(o,{label:"强制密码复杂度"},{default:l(()=>[e(w,{modelValue:n.enforcePasswordComplexity,"onUpdate:modelValue":a[46]||(a[46]=t=>n.enforcePasswordComplexity=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:l(()=>[e(o,{label:"双因子认证"},{default:l(()=>[e(w,{modelValue:n.twoFactorAuth,"onUpdate:modelValue":a[47]||(a[47]=t=>n.twoFactorAuth=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:l(()=>[e(o,{label:"IP白名单"},{default:l(()=>[e(w,{modelValue:n.ipWhitelist,"onUpdate:modelValue":a[48]||(a[48]=t=>n.ipWhitelist=t),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),n.ipWhitelist?(q(),F(o,{key:0,label:"允许的IP地址"},{default:l(()=>[e(f,{modelValue:n.allowedIPs,"onUpdate:modelValue":a[49]||(a[49]=t=>n.allowedIPs=t),type:"textarea",rows:3,placeholder:"每行一个IP地址或IP段，例如: ***********/24"},null,8,["modelValue"])]),_:1})):W("",!0)]),_:1},8,["model"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),T.value?(q(),F(ee,{key:0,title:"有未保存的配置更改",type:"warning","show-icon":"",class:"save-alert"},{default:l(()=>[a[69]||(a[69]=v(" 您有未保存的配置更改，请及时保存以避免丢失。 ")),e(x,{type:"text",onClick:E,loading:h.value},{default:l(()=>a[68]||(a[68]=[v(" 立即保存 ")])),_:1,__:[68]},8,["loading"])]),_:1})):W("",!0)])}}}),Te=me(xe,[["__scopeId","data-v-aac9e148"]]);export{Te as default};
