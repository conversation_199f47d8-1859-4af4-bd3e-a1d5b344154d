import{d as xe,r as p,a as E,o as Ue,c as U,e as n,f as e,w as t,j as u,n as s,h as g,F as Se,af as J,G as We,N as Q,I as ze,g as De,S as O,T as I,H as Te,K as $e,O as he,P as T,t as y,U as Fe,D as Le,J as K,p as b,C as X,k as f}from"./index-BDkHONMN.js";import{_ as Me}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Re={class:"sensitive-word-container"},je={class:"toolbar"},Be={class:"toolbar-left"},Ne={class:"toolbar-right"},Ee={class:"pagination-wrapper"},Oe={class:"dialog-footer"},Ie={class:"dialog-footer"},Ke={class:"test-result"},Pe={class:"result-summary"},qe={key:0},Ae={key:0,class:"sensitive-list"},Ge={class:"filtered-text"},He={class:"text-content"},Je=xe({__name:"SensitiveWordManager",setup(Qe){const M=p(!1),P=p([]),$=p([]),q=p(["政治敏感","暴力血腥","色情低俗","广告垃圾","其他"]),_=E({keyword:"",category:"",level:"",status:""}),C=E({page:1,pageSize:20,total:0}),k=p(!1),S=p(!1),R=p(!1),h=p(!1),A=p(),r=E({id:null,word:"",category:"",level:1,replacement:"",description:"",status:1}),Y={word:[{required:!0,message:"请输入敏感词",trigger:"blur"}],level:[{required:!0,message:"请选择风险级别",trigger:"change"}]},Z=p(),F=p([]),x=p(""),m=p(null),v=async()=>{M.value=!0;try{await new Promise(o=>setTimeout(o,500)),P.value=[{id:1,word:"测试敏感词1",category:"政治敏感",level:3,replacement:"***",hitCount:15,status:1,createTime:new Date},{id:2,word:"测试敏感词2",category:"暴力血腥",level:2,replacement:"【屏蔽】",hitCount:8,status:1,createTime:new Date}],C.total=50}finally{M.value=!1}},ee=()=>{Object.assign(_,{keyword:"",category:"",level:"",status:""}),v()},le=o=>{$.value=o},te=o=>({1:"success",2:"warning",3:"danger"})[o]||"info",ae=o=>({1:"低风险",2:"中风险",3:"高风险"})[o]||"未知",oe=o=>new Date(o).toLocaleString(),se=async o=>{try{b.success(`敏感词已${o.status===1?"启用":"禁用"}`)}catch{o.status=o.status===1?0:1,b.error("状态更新失败")}},ne=()=>{h.value=!1,ie(),k.value=!0},ue=o=>{h.value=!0,Object.assign(r,o),k.value=!0},de=async o=>{try{await X.confirm(`确定要删除敏感词"${o.word}"吗？`,"提示",{type:"warning"}),b.success("删除成功"),v()}catch{}},re=async()=>{try{await X.confirm(`确定要删除选中的 ${$.value.length} 个敏感词吗？`,"提示",{type:"warning"}),b.success("批量删除成功"),$.value=[],v()}catch{}},ie=()=>{Object.assign(r,{id:null,word:"",category:"",level:1,replacement:"",description:"",status:1})},pe=async()=>{var o;try{await((o=A.value)==null?void 0:o.validate()),b.success(h.value?"敏感词更新成功":"敏感词添加成功"),k.value=!1,v()}catch{}},fe=()=>{F.value=[],S.value=!0},_e=o=>{F.value=[o]},me=async()=>{if(F.value.length===0){b.warning("请选择要导入的文件");return}b.success("导入成功"),S.value=!1,v()},ve=()=>{b.success("导出成功")},ce=o=>{x.value=`这是一个包含${o.word}的测试文本`,m.value=null,R.value=!0},ge=()=>{if(!x.value.trim()){b.warning("请输入测试文本");return}const o=["测试敏感词1","测试敏感词2"].filter(l=>x.value.includes(l));m.value={hasSensitive:o.length>0,sensitiveWords:o,filteredText:x.value.replace(/测试敏感词\d+/g,"***")}},ye=()=>{b.success("敏感词缓存刷新成功")};return Ue(()=>{v()}),(o,l)=>{const c=u("el-icon"),d=u("el-button"),W=u("el-input"),i=u("el-form-item"),V=u("el-option"),L=u("el-select"),j=u("el-form"),G=u("el-card"),w=u("el-table-column"),z=u("el-tag"),H=u("el-switch"),be=u("el-table"),we=u("el-pagination"),B=u("el-radio"),Ve=u("el-radio-group"),N=u("el-dialog"),Ce=u("el-upload"),ke=he("loading");return f(),U("div",Re,[l[39]||(l[39]=n("div",{class:"page-header"},[n("h1",null,"敏感词管理"),n("p",null,"管理系统敏感词库，维护内容安全")],-1)),n("div",je,[n("div",Be,[e(d,{type:"primary",onClick:ne},{default:t(()=>[e(c,null,{default:t(()=>[e(g(Se))]),_:1}),l[18]||(l[18]=s(" 添加敏感词 "))]),_:1,__:[18]}),e(d,{onClick:fe},{default:t(()=>[e(c,null,{default:t(()=>[e(g(J))]),_:1}),l[19]||(l[19]=s(" 批量导入 "))]),_:1,__:[19]}),e(d,{onClick:ve},{default:t(()=>[e(c,null,{default:t(()=>[e(g(We))]),_:1}),l[20]||(l[20]=s(" 导出 "))]),_:1,__:[20]}),e(d,{type:"danger",disabled:$.value.length===0,onClick:re},{default:t(()=>[e(c,null,{default:t(()=>[e(g(Q))]),_:1}),l[21]||(l[21]=s(" 批量删除 "))]),_:1,__:[21]},8,["disabled"])]),n("div",Ne,[e(d,{onClick:ye,type:"warning"},{default:t(()=>[e(c,null,{default:t(()=>[e(g(ze))]),_:1}),l[22]||(l[22]=s(" 刷新缓存 "))]),_:1,__:[22]})])]),e(G,{class:"filter-card"},{default:t(()=>[e(j,{model:_,inline:""},{default:t(()=>[e(i,{label:"关键词"},{default:t(()=>[e(W,{modelValue:_.keyword,"onUpdate:modelValue":l[0]||(l[0]=a=>_.keyword=a),placeholder:"请输入敏感词",clearable:"",onKeyup:De(v,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"分类"},{default:t(()=>[e(L,{modelValue:_.category,"onUpdate:modelValue":l[1]||(l[1]=a=>_.category=a),placeholder:"请选择分类",clearable:""},{default:t(()=>[(f(!0),U(O,null,I(q.value,a=>(f(),T(V,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"级别"},{default:t(()=>[e(L,{modelValue:_.level,"onUpdate:modelValue":l[2]||(l[2]=a=>_.level=a),placeholder:"请选择级别",clearable:""},{default:t(()=>[e(V,{label:"低风险",value:1}),e(V,{label:"中风险",value:2}),e(V,{label:"高风险",value:3})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"状态"},{default:t(()=>[e(L,{modelValue:_.status,"onUpdate:modelValue":l[3]||(l[3]=a=>_.status=a),placeholder:"请选择状态",clearable:""},{default:t(()=>[e(V,{label:"启用",value:1}),e(V,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),e(i,null,{default:t(()=>[e(d,{type:"primary",onClick:v},{default:t(()=>[e(c,null,{default:t(()=>[e(g(Te))]),_:1}),l[23]||(l[23]=s(" 搜索 "))]),_:1,__:[23]}),e(d,{onClick:ee},{default:t(()=>l[24]||(l[24]=[s("重置")])),_:1,__:[24]})]),_:1})]),_:1},8,["model"])]),_:1}),e(G,null,{default:t(()=>[$e((f(),T(be,{data:P.value,onSelectionChange:le,stripe:""},{default:t(()=>[e(w,{type:"selection",width:"55"}),e(w,{prop:"word",label:"敏感词","min-width":"120"},{default:t(({row:a})=>[e(z,null,{default:t(()=>[s(y(a.word),1)]),_:2},1024)]),_:1}),e(w,{prop:"category",label:"分类",width:"100"},{default:t(({row:a})=>[e(z,{type:"info",size:"small"},{default:t(()=>[s(y(a.category||"未分类"),1)]),_:2},1024)]),_:1}),e(w,{prop:"level",label:"风险级别",width:"100"},{default:t(({row:a})=>[e(z,{type:te(a.level),size:"small"},{default:t(()=>[s(y(ae(a.level)),1)]),_:2},1032,["type"])]),_:1}),e(w,{prop:"replacement",label:"替换词",width:"120"},{default:t(({row:a})=>[n("span",null,y(a.replacement||"-"),1)]),_:1}),e(w,{prop:"hitCount",label:"命中次数",width:"100",align:"right"},{default:t(({row:a})=>[n("span",null,y(a.hitCount||0),1)]),_:1}),e(w,{prop:"status",label:"状态",width:"80"},{default:t(({row:a})=>[e(H,{modelValue:a.status,"onUpdate:modelValue":D=>a.status=D,"active-value":1,"inactive-value":0,onChange:D=>se(a)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(w,{prop:"createTime",label:"创建时间",width:"160"},{default:t(({row:a})=>[s(y(oe(a.createTime)),1)]),_:1}),e(w,{label:"操作",width:"200",fixed:"right"},{default:t(({row:a})=>[e(d,{type:"text",size:"small",onClick:D=>ue(a)},{default:t(()=>[e(c,null,{default:t(()=>[e(g(Fe))]),_:1}),l[25]||(l[25]=s(" 编辑 "))]),_:2,__:[25]},1032,["onClick"]),e(d,{type:"text",size:"small",onClick:D=>ce(a)},{default:t(()=>[e(c,null,{default:t(()=>[e(g(Le))]),_:1}),l[26]||(l[26]=s(" 测试 "))]),_:2,__:[26]},1032,["onClick"]),e(d,{type:"text",size:"small",onClick:D=>de(a),style:{color:"#f56c6c"}},{default:t(()=>[e(c,null,{default:t(()=>[e(g(Q))]),_:1}),l[27]||(l[27]=s(" 删除 "))]),_:2,__:[27]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ke,M.value]]),n("div",Ee,[e(we,{"current-page":C.page,"onUpdate:currentPage":l[4]||(l[4]=a=>C.page=a),"page-size":C.pageSize,"onUpdate:pageSize":l[5]||(l[5]=a=>C.pageSize=a),total:C.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:v,onCurrentChange:v},null,8,["current-page","page-size","total"])])]),_:1}),e(N,{modelValue:k.value,"onUpdate:modelValue":l[13]||(l[13]=a=>k.value=a),title:h.value?"编辑敏感词":"添加敏感词",width:"600px"},{footer:t(()=>[n("div",Oe,[e(d,{onClick:l[12]||(l[12]=a=>k.value=!1)},{default:t(()=>l[31]||(l[31]=[s("取消")])),_:1,__:[31]}),e(d,{type:"primary",onClick:pe},{default:t(()=>l[32]||(l[32]=[s("确定")])),_:1,__:[32]})])]),default:t(()=>[e(j,{ref_key:"wordFormRef",ref:A,model:r,rules:Y,"label-width":"80px"},{default:t(()=>[e(i,{label:"敏感词",prop:"word"},{default:t(()=>[e(W,{modelValue:r.word,"onUpdate:modelValue":l[6]||(l[6]=a=>r.word=a),placeholder:"请输入敏感词"},null,8,["modelValue"])]),_:1}),e(i,{label:"分类",prop:"category"},{default:t(()=>[e(L,{modelValue:r.category,"onUpdate:modelValue":l[7]||(l[7]=a=>r.category=a),placeholder:"请选择分类","allow-create":"",filterable:""},{default:t(()=>[(f(!0),U(O,null,I(q.value,a=>(f(),T(V,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"风险级别",prop:"level"},{default:t(()=>[e(Ve,{modelValue:r.level,"onUpdate:modelValue":l[8]||(l[8]=a=>r.level=a)},{default:t(()=>[e(B,{label:1},{default:t(()=>l[28]||(l[28]=[s("低风险")])),_:1,__:[28]}),e(B,{label:2},{default:t(()=>l[29]||(l[29]=[s("中风险")])),_:1,__:[29]}),e(B,{label:3},{default:t(()=>l[30]||(l[30]=[s("高风险")])),_:1,__:[30]})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"替换词"},{default:t(()=>[e(W,{modelValue:r.replacement,"onUpdate:modelValue":l[9]||(l[9]=a=>r.replacement=a),placeholder:"可选，检测到时的替换内容"},null,8,["modelValue"])]),_:1}),e(i,{label:"描述"},{default:t(()=>[e(W,{modelValue:r.description,"onUpdate:modelValue":l[10]||(l[10]=a=>r.description=a),type:"textarea",placeholder:"请输入描述",rows:3},null,8,["modelValue"])]),_:1}),e(i,{label:"状态"},{default:t(()=>[e(H,{modelValue:r.status,"onUpdate:modelValue":l[11]||(l[11]=a=>r.status=a),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(N,{modelValue:S.value,"onUpdate:modelValue":l[15]||(l[15]=a=>S.value=a),title:"批量导入敏感词",width:"500px"},{footer:t(()=>[n("div",Ie,[e(d,{onClick:l[14]||(l[14]=a=>S.value=!1)},{default:t(()=>l[35]||(l[35]=[s("取消")])),_:1,__:[35]}),e(d,{type:"primary",onClick:me},{default:t(()=>l[36]||(l[36]=[s("确定导入")])),_:1,__:[36]})])]),default:t(()=>[e(Ce,{ref_key:"uploadRef",ref:Z,"auto-upload":!1,"file-list":F.value,accept:".txt,.csv",onChange:_e},{tip:t(()=>l[34]||(l[34]=[n("div",{class:"upload-tip"}," 支持 .txt 和 .csv 格式，每行一个敏感词 ",-1)])),default:t(()=>[e(d,{type:"primary"},{default:t(()=>[e(c,null,{default:t(()=>[e(g(J))]),_:1}),l[33]||(l[33]=s(" 选择文件 "))]),_:1,__:[33]})]),_:1},8,["file-list"])]),_:1},8,["modelValue"]),e(N,{modelValue:R.value,"onUpdate:modelValue":l[17]||(l[17]=a=>R.value=a),title:"敏感词测试",width:"600px"},{default:t(()=>[e(j,{"label-width":"80px"},{default:t(()=>[e(i,{label:"测试文本"},{default:t(()=>[e(W,{modelValue:x.value,"onUpdate:modelValue":l[16]||(l[16]=a=>x.value=a),type:"textarea",placeholder:"请输入要检测的文本",rows:5},null,8,["modelValue"])]),_:1}),e(i,null,{default:t(()=>[e(d,{type:"primary",onClick:ge},{default:t(()=>l[37]||(l[37]=[s("开始检测")])),_:1,__:[37]})]),_:1}),m.value?(f(),T(i,{key:0,label:"检测结果"},{default:t(()=>[n("div",Ke,[n("div",Pe,[e(z,{type:m.value.hasSensitive?"danger":"success"},{default:t(()=>[s(y(m.value.hasSensitive?"发现敏感词":"未发现敏感词"),1)]),_:1},8,["type"]),m.value.sensitiveWords.length>0?(f(),U("span",qe," 共发现 "+y(m.value.sensitiveWords.length)+" 个敏感词 ",1)):K("",!0)]),m.value.sensitiveWords.length>0?(f(),U("div",Ae,[(f(!0),U(O,null,I(m.value.sensitiveWords,a=>(f(),T(z,{key:a,type:"danger",style:{margin:"2px"}},{default:t(()=>[s(y(a),1)]),_:2},1024))),128))])):K("",!0),n("div",Ge,[l[38]||(l[38]=n("h4",null,"过滤后文本：",-1)),n("div",He,y(m.value.filteredText),1)])])]),_:1})):K("",!0)]),_:1})]),_:1},8,["modelValue"])])}}}),Ze=Me(Je,[["__scopeId","data-v-8a7f31cd"]]);export{Ze as default};
