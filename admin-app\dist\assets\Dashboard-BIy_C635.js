import{d as b,c as g,e as s,f as a,w as l,j as i,h as d,l as p,z as v,D as m,B as k,n as _,v as $,k as C}from"./index-BDkHONMN.js";import{_ as w}from"./_plugin-vue_export-helper-DlAUqK2U.js";const y={class:"dashboard-container"},B={class:"stats-grid"},D={class:"stats-content"},N={class:"stats-icon"},V={class:"stats-content"},h={class:"stats-icon"},j={class:"stats-content"},q={class:"stats-icon"},x={class:"stats-content"},z={class:"stats-icon"},E={class:"charts-grid"},I={class:"quick-actions"},T={class:"actions-grid"},A=b({__name:"Dashboard",setup(F){return(r,t)=>{const e=i("el-icon"),n=i("el-card"),o=i("el-col"),c=i("el-row"),u=i("el-button");return C(),g("div",y,[t[17]||(t[17]=s("div",{class:"dashboard-header"},[s("h1",null,"控制台"),s("p",null,"欢迎使用食谱管理系统")],-1)),s("div",B,[a(c,{gutter:20},{default:l(()=>[a(o,{span:6},{default:l(()=>[a(n,{class:"stats-card"},{default:l(()=>[s("div",D,[s("div",N,[a(e,{color:"#409eff"},{default:l(()=>[a(d(p))]),_:1})]),t[4]||(t[4]=s("div",{class:"stats-info"},[s("div",{class:"stats-number"},"1,234"),s("div",{class:"stats-label"},"用户总数")],-1))])]),_:1})]),_:1}),a(o,{span:6},{default:l(()=>[a(n,{class:"stats-card"},{default:l(()=>[s("div",V,[s("div",h,[a(e,{color:"#67c23a"},{default:l(()=>[a(d(v))]),_:1})]),t[5]||(t[5]=s("div",{class:"stats-info"},[s("div",{class:"stats-number"},"5,678"),s("div",{class:"stats-label"},"食谱总数")],-1))])]),_:1})]),_:1}),a(o,{span:6},{default:l(()=>[a(n,{class:"stats-card"},{default:l(()=>[s("div",j,[s("div",q,[a(e,{color:"#e6a23c"},{default:l(()=>[a(d(m))]),_:1})]),t[6]||(t[6]=s("div",{class:"stats-info"},[s("div",{class:"stats-number"},"12,345"),s("div",{class:"stats-label"},"今日访问")],-1))])]),_:1})]),_:1}),a(o,{span:6},{default:l(()=>[a(n,{class:"stats-card"},{default:l(()=>[s("div",x,[s("div",z,[a(e,{color:"#f56c6c"},{default:l(()=>[a(d(k))]),_:1})]),t[7]||(t[7]=s("div",{class:"stats-info"},[s("div",{class:"stats-number"},"890"),s("div",{class:"stats-label"},"待审核")],-1))])]),_:1})]),_:1})]),_:1})]),s("div",E,[a(c,{gutter:20},{default:l(()=>[a(o,{span:12},{default:l(()=>[a(n,null,{header:l(()=>t[8]||(t[8]=[s("div",{class:"card-header"},[s("span",null,"用户增长趋势")],-1)])),default:l(()=>[t[9]||(t[9]=s("div",{class:"chart-placeholder"},[s("p",null,"用户增长图表区域")],-1))]),_:1,__:[9]})]),_:1}),a(o,{span:12},{default:l(()=>[a(n,null,{header:l(()=>t[10]||(t[10]=[s("div",{class:"card-header"},[s("span",null,"内容发布趋势")],-1)])),default:l(()=>[t[11]||(t[11]=s("div",{class:"chart-placeholder"},[s("p",null,"内容发布图表区域")],-1))]),_:1,__:[11]})]),_:1})]),_:1})]),s("div",I,[a(n,null,{header:l(()=>t[12]||(t[12]=[s("div",{class:"card-header"},[s("span",null,"快速操作")],-1)])),default:l(()=>[s("div",T,[a(u,{type:"primary",onClick:t[0]||(t[0]=f=>r.$router.push("/users"))},{default:l(()=>[a(e,null,{default:l(()=>[a(d(p))]),_:1}),t[13]||(t[13]=_(" 用户管理 "))]),_:1,__:[13]}),a(u,{type:"success",onClick:t[1]||(t[1]=f=>r.$router.push("/content/recipes"))},{default:l(()=>[a(e,null,{default:l(()=>[a(d(v))]),_:1}),t[14]||(t[14]=_(" 食谱管理 "))]),_:1,__:[14]}),a(u,{type:"warning",onClick:t[2]||(t[2]=f=>r.$router.push("/content/audit"))},{default:l(()=>[a(e,null,{default:l(()=>[a(d(m))]),_:1}),t[15]||(t[15]=_(" 内容审核 "))]),_:1,__:[15]}),a(u,{type:"info",onClick:t[3]||(t[3]=f=>r.$router.push("/system/config"))},{default:l(()=>[a(e,null,{default:l(()=>[a(d($))]),_:1}),t[16]||(t[16]=_(" 系统设置 "))]),_:1,__:[16]})])]),_:1})])])}}}),J=w(A,[["__scopeId","data-v-acaae61b"]]);export{J as default};
