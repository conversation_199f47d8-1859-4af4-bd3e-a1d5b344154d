import{d as ve,r as b,a as A,X as fe,R as ge,a2 as ye,z as be,o as he,p as w,c as T,e as n,f as e,w as a,j as s,S as Ce,T as ke,n as i,h as p,H as we,I as Ve,J as K,K as ze,t as d,L as R,M as Y,O as Me,P as $,Q as Te,D as xe,s as Se,C as je,k as f,_ as $e,Z as De}from"./index-BDkHONMN.js";import{_ as Ue}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ae={class:"content-audit-page"},Re={class:"stats-cards"},Ye={class:"stat-content"},Le={class:"stat-info"},Be={class:"stat-value"},Ie={class:"stat-label"},Ne={key:0,class:"batch-toolbar"},Ee={class:"selected-text"},Fe={class:"batch-actions"},He={class:"content-info"},Oe={class:"content-cover"},Pe=["src"],qe={class:"content-details"},Je={class:"content-title"},Ke={class:"content-type"},Qe={class:"content-desc"},Xe={class:"author-info"},Ze={class:"author-details"},Ge={class:"author-name"},We={class:"author-username"},et={class:"stats-info"},tt={class:"time-info"},lt={class:"actions"},at={key:1,class:"audit-result"},nt={class:"pagination-wrapper"},ot={key:0,class:"content-preview"},st={class:"preview-header"},dt=["src"],it={class:"preview-info"},ut={class:"preview-meta"},rt={class:"preview-content"},ct=["innerHTML"],_t={class:"dialog-footer"},pt={class:"dialog-footer"},mt=ve({__name:"ContentAudit",setup(vt){const x=b(!1),L=b([]),V=b([]),z=b(!1),u=b(null),M=b(!1),B=b(null),m=A({keyword:"",contentType:null,status:0,dateRange:[]}),v=A({page:1,pageSize:20,total:0}),g=A({reason:"",comment:""}),Q=b([{key:"pending",label:"待审核",value:25,icon:fe},{key:"approved",label:"已通过",value:156,icon:ge},{key:"rejected",label:"已拒绝",value:12,icon:ye},{key:"total",label:"总内容",value:193,icon:be}]);he(()=>{k(),X()});const k=async()=>{x.value=!0;try{L.value=[{id:1,title:"红烧肉的做法",description:"正宗的红烧肉制作方法，香甜可口",coverImage:"/recipe-1.jpg",contentType:"recipe",status:0,author:{id:1,username:"chef001",nickname:"美食达人",avatar:""},viewCount:0,likeCount:0,favoriteCount:0,createdAt:"2024-01-20T10:30:00"}],v.total=1}catch{w.error("加载内容列表失败")}finally{x.value=!1}},X=async()=>{},I=()=>{v.page=1,k()},Z=()=>{Object.assign(m,{keyword:"",contentType:null,status:0,dateRange:[]}),I()},G=o=>{V.value=o},N=async o=>{if(V.value.length===0){w.warning("请选择要审核的内容");return}const t=o===1?"通过":"拒绝";try{await je.confirm(`确定要${t}选中的内容吗？`,"提示",{type:"warning"}),w.success(`批量${t}成功`),k(),V.value=[]}catch{}},W=async o=>{const{action:t,content:y}=o;switch(t){case"approve":await S(y,1);break;case"reject":B.value=y,M.value=!0;break}},S=async(o,t,y)=>{try{const j=t===1?"通过":"拒绝";w.success(`内容${j}成功`),z.value=!1,k()}catch{w.error("审核失败")}},ee=async()=>{if(!g.reason){w.warning("请选择拒绝原因");return}await S(B.value,2,g.comment),M.value=!1,Object.assign(g,{reason:"",comment:""})},te=o=>{u.value=o,z.value=!0},le=o=>{v.page=o,k()},ae=o=>{v.pageSize=o,v.page=1,k()},ne=o=>({0:"warning",1:"success",2:"danger"})[o]||"info",oe=o=>({0:"待审核",1:"已通过",2:"已拒绝"})[o]||"未知",se=o=>({recipe:"success",article:"primary"})[o]||"info",E=o=>({recipe:"食谱",article:"文章"})[o]||o,D=o=>new Date(o).toLocaleString("zh-CN");return(o,t)=>{var J;const y=s("el-card"),j=s("el-col"),de=s("el-row"),F=s("el-input"),h=s("el-form-item"),r=s("el-option"),U=s("el-select"),ie=s("el-date-picker"),c=s("el-icon"),_=s("el-button"),H=s("el-form"),C=s("el-table-column"),O=s("el-tag"),ue=s("el-avatar"),P=s("el-dropdown-item"),re=s("el-dropdown-menu"),ce=s("el-dropdown"),_e=s("el-table"),pe=s("el-pagination"),q=s("el-dialog"),me=Me("loading");return f(),T("div",Ae,[t[29]||(t[29]=n("div",{class:"page-header"},[n("h2",null,"内容审核"),n("p",null,"审核用户提交的食谱和文章内容")],-1)),n("div",Re,[e(de,{gutter:20},{default:a(()=>[(f(!0),T(Ce,null,ke(Q.value,l=>(f(),$(j,{xs:24,sm:12,md:6,key:l.key},{default:a(()=>[e(y,{class:"stat-card"},{default:a(()=>[n("div",Ye,[n("div",{class:$e(["stat-icon",`stat-${l.key}`])},[(f(),$(De(l.icon)))],2),n("div",Le,[n("div",Be,d(l.value),1),n("div",Ie,d(l.label),1)])])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),e(y,{class:"toolbar-card"},{default:a(()=>[e(H,{model:m,inline:!0,class:"search-form"},{default:a(()=>[e(h,{label:"关键词"},{default:a(()=>[e(F,{modelValue:m.keyword,"onUpdate:modelValue":t[0]||(t[0]=l=>m.keyword=l),placeholder:"搜索标题、描述",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(h,{label:"内容类型"},{default:a(()=>[e(U,{modelValue:m.contentType,"onUpdate:modelValue":t[1]||(t[1]=l=>m.contentType=l),placeholder:"选择类型",clearable:""},{default:a(()=>[e(r,{label:"全部",value:null}),e(r,{label:"食谱",value:"recipe"}),e(r,{label:"文章",value:"article"})]),_:1},8,["modelValue"])]),_:1}),e(h,{label:"状态"},{default:a(()=>[e(U,{modelValue:m.status,"onUpdate:modelValue":t[2]||(t[2]=l=>m.status=l),placeholder:"选择状态",clearable:""},{default:a(()=>[e(r,{label:"全部",value:null}),e(r,{label:"待审核",value:0}),e(r,{label:"已通过",value:1}),e(r,{label:"已拒绝",value:2})]),_:1},8,["modelValue"])]),_:1}),e(h,{label:"提交时间"},{default:a(()=>[e(ie,{modelValue:m.dateRange,"onUpdate:modelValue":t[3]||(t[3]=l=>m.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(h,null,{default:a(()=>[e(_,{type:"primary",onClick:I,loading:x.value},{default:a(()=>[e(c,null,{default:a(()=>[e(p(we))]),_:1}),t[16]||(t[16]=i(" 搜索 "))]),_:1,__:[16]},8,["loading"]),e(_,{onClick:Z},{default:a(()=>[e(c,null,{default:a(()=>[e(p(Ve))]),_:1}),t[17]||(t[17]=i(" 重置 "))]),_:1,__:[17]})]),_:1})]),_:1},8,["model"])]),_:1}),e(y,{class:"table-card"},{default:a(()=>[V.value.length>0?(f(),T("div",Ne,[n("span",Ee,"已选择 "+d(V.value.length)+" 个内容",1),n("div",Fe,[e(_,{type:"success",size:"small",onClick:t[4]||(t[4]=l=>N(1))},{default:a(()=>[e(c,null,{default:a(()=>[e(p(R))]),_:1}),t[18]||(t[18]=i(" 批量通过 "))]),_:1,__:[18]}),e(_,{type:"danger",size:"small",onClick:t[5]||(t[5]=l=>N(2))},{default:a(()=>[e(c,null,{default:a(()=>[e(p(Y))]),_:1}),t[19]||(t[19]=i(" 批量拒绝 "))]),_:1,__:[19]})])])):K("",!0),ze((f(),$(_e,{data:L.value,onSelectionChange:G,"row-key":"id",class:"content-table"},{default:a(()=>[e(C,{type:"selection",width:"55"}),e(C,{label:"内容信息","min-width":"300"},{default:a(({row:l})=>[n("div",He,[n("div",Oe,[n("img",{src:l.coverImage||"/default-recipe.jpg",alt:"封面"},null,8,Pe)]),n("div",qe,[n("div",Je,d(l.title),1),n("div",Ke,[e(O,{size:"small",type:se(l.contentType)},{default:a(()=>[i(d(E(l.contentType)),1)]),_:2},1032,["type"])]),n("div",Qe,d(l.description||"暂无描述"),1)])])]),_:1}),e(C,{label:"作者信息",width:"150"},{default:a(({row:l})=>[n("div",Xe,[e(ue,{size:32,src:l.author.avatar},{default:a(()=>[e(c,null,{default:a(()=>[e(p(Te))]),_:1})]),_:2},1032,["src"]),n("div",Ze,[n("div",Ge,d(l.author.nickname||l.author.username),1),n("div",We,"@"+d(l.author.username),1)])])]),_:1}),e(C,{label:"状态",width:"100"},{default:a(({row:l})=>[e(O,{type:ne(l.status),size:"small"},{default:a(()=>[i(d(oe(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(C,{label:"统计信息",width:"120"},{default:a(({row:l})=>[n("div",et,[n("div",null,"浏览: "+d(l.viewCount||0),1),n("div",null,"点赞: "+d(l.likeCount||0),1),n("div",null,"收藏: "+d(l.favoriteCount||0),1)])]),_:1}),e(C,{label:"提交时间",width:"160"},{default:a(({row:l})=>[n("div",tt,d(D(l.createdAt)),1)]),_:1}),e(C,{label:"操作",width:"200",fixed:"right"},{default:a(({row:l})=>[n("div",lt,[e(_,{type:"primary",size:"small",onClick:ft=>te(l)},{default:a(()=>[e(c,null,{default:a(()=>[e(p(xe))]),_:1}),t[20]||(t[20]=i(" 预览 "))]),_:2,__:[20]},1032,["onClick"]),l.status===0?(f(),$(ce,{key:0,onCommand:W},{dropdown:a(()=>[e(re,null,{default:a(()=>[e(P,{command:{action:"approve",content:l}},{default:a(()=>[e(c,null,{default:a(()=>[e(p(R))]),_:1}),t[22]||(t[22]=i(" 通过审核 "))]),_:2,__:[22]},1032,["command"]),e(P,{command:{action:"reject",content:l},divided:""},{default:a(()=>[e(c,null,{default:a(()=>[e(p(Y))]),_:1}),t[23]||(t[23]=i(" 拒绝审核 "))]),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:a(()=>[e(_,{type:"text",size:"small"},{default:a(()=>[t[21]||(t[21]=i(" 审核")),e(c,{class:"el-icon--right"},{default:a(()=>[e(p(Se))]),_:1})]),_:1,__:[21]})]),_:2},1024)):(f(),T("span",at,d(l.auditor)+" - "+d(D(l.auditedAt)),1))])]),_:1})]),_:1},8,["data"])),[[me,x.value]]),n("div",nt,[e(pe,{"current-page":v.page,"onUpdate:currentPage":t[6]||(t[6]=l=>v.page=l),"page-size":v.pageSize,"onUpdate:pageSize":t[7]||(t[7]=l=>v.pageSize=l),total:v.total,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ae,onCurrentChange:le},null,8,["current-page","page-size","total"])])]),_:1}),e(q,{modelValue:z.value,"onUpdate:modelValue":t[11]||(t[11]=l=>z.value=l),title:`预览 - ${(J=u.value)==null?void 0:J.title}`,width:"80%",top:"5vh"},{footer:a(()=>[n("span",_t,[e(_,{onClick:t[8]||(t[8]=l=>z.value=!1)},{default:a(()=>t[24]||(t[24]=[i("关闭")])),_:1,__:[24]}),e(_,{type:"success",onClick:t[9]||(t[9]=l=>S(u.value,1))},{default:a(()=>[e(c,null,{default:a(()=>[e(p(R))]),_:1}),t[25]||(t[25]=i(" 通过审核 "))]),_:1,__:[25]}),e(_,{type:"danger",onClick:t[10]||(t[10]=l=>S(u.value,2))},{default:a(()=>[e(c,null,{default:a(()=>[e(p(Y))]),_:1}),t[26]||(t[26]=i(" 拒绝审核 "))]),_:1,__:[26]})])]),default:a(()=>[u.value?(f(),T("div",ot,[n("div",st,[n("img",{src:u.value.coverImage,alt:"封面",class:"preview-cover"},null,8,dt),n("div",it,[n("h3",null,d(u.value.title),1),n("p",null,d(u.value.description),1),n("div",ut,[n("span",null,"作者: "+d(u.value.author.nickname),1),n("span",null,"类型: "+d(E(u.value.contentType)),1),n("span",null,"提交时间: "+d(D(u.value.createdAt)),1)])])]),n("div",rt,[n("div",{innerHTML:u.value.content||"暂无内容"},null,8,ct)])])):K("",!0)]),_:1},8,["modelValue","title"]),e(q,{modelValue:M.value,"onUpdate:modelValue":t[15]||(t[15]=l=>M.value=l),title:"拒绝审核",width:"500px"},{footer:a(()=>[n("span",pt,[e(_,{onClick:t[14]||(t[14]=l=>M.value=!1)},{default:a(()=>t[27]||(t[27]=[i("取消")])),_:1,__:[27]}),e(_,{type:"danger",onClick:ee},{default:a(()=>t[28]||(t[28]=[i("确认拒绝")])),_:1,__:[28]})])]),default:a(()=>[e(H,{model:g,"label-width":"80px"},{default:a(()=>[e(h,{label:"拒绝原因"},{default:a(()=>[e(U,{modelValue:g.reason,"onUpdate:modelValue":t[12]||(t[12]=l=>g.reason=l),placeholder:"选择拒绝原因"},{default:a(()=>[e(r,{label:"内容不当",value:"inappropriate"}),e(r,{label:"违规信息",value:"violation"}),e(r,{label:"质量不达标",value:"quality"}),e(r,{label:"其他原因",value:"other"})]),_:1},8,["modelValue"])]),_:1}),e(h,{label:"详细说明"},{default:a(()=>[e(F,{modelValue:g.comment,"onUpdate:modelValue":t[13]||(t[13]=l=>g.comment=l),type:"textarea",rows:4,placeholder:"请详细说明拒绝原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),bt=Ue(mt,[["__scopeId","data-v-0b3cbc18"]]);export{bt as default};
