import{d as f,c as i,e as t,f as o,w as n,j as d,h as a,z as p,n as _,ax as m,ay as v,b as x,k as h}from"./index-BDkHONMN.js";import{_ as k}from"./_plugin-vue_export-helper-DlAUqK2U.js";const C={class:"not-found-container"},N={class:"not-found-content"},b={class:"not-found-image"},g={class:"not-found-actions"},B=f({__name:"NotFound",setup(w){const c=x(),u=()=>{c.push("/dashboard")},r=()=>{c.go(-1)};return(y,e)=>{const s=d("el-icon"),l=d("el-button");return h(),i("div",C,[t("div",N,[t("div",b,[o(s,{size:"120",color:"#c0c4cc"},{default:n(()=>[o(a(p))]),_:1})]),e[2]||(e[2]=t("div",{class:"not-found-text"},[t("h1",null,"404"),t("h2",null,"页面不存在"),t("p",null,"抱歉，您访问的页面不存在或已被删除。")],-1)),t("div",g,[o(l,{type:"primary",onClick:u},{default:n(()=>[o(s,null,{default:n(()=>[o(a(m))]),_:1}),e[0]||(e[0]=_(" 返回首页 "))]),_:1,__:[0]}),o(l,{onClick:r},{default:n(()=>[o(s,null,{default:n(()=>[o(a(v))]),_:1}),e[1]||(e[1]=_(" 返回上页 "))]),_:1,__:[1]})])])])}}}),F=k(B,[["__scopeId","data-v-fca3c38c"]]);export{F as default};
