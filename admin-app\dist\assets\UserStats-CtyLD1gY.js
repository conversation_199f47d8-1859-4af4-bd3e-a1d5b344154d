import{d as xe,r as i,l as Ue,aj as Se,ak as De,al as Re,o as Ae,ag as ze,ah as Te,p as h,c as Z,e as s,f as e,w as t,j as d,n as u,h as U,G as Ve,I as Pe,S as Be,T as Le,t as r,_ as b,Q as Me,b as Oe,k as C,P as A,am as Ne,Z as Fe,ai as Ge,s as $e}from"./index-BDkHONMN.js";import{i as S,L as q}from"./index-BNz9ATsT.js";import{_ as je}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ee={class:"user-stats-page"},Ie={class:"page-header"},We={class:"header-actions"},Xe={class:"metric-content"},Ke={class:"metric-info"},Qe={class:"metric-value"},Ze={class:"metric-label"},qe={class:"metric-trend"},He={class:"chart-header"},Je={class:"table-header"},Ye={class:"table-header"},et={class:"ranking-header"},tt={class:"ranking-tabs"},at={class:"user-info"},lt={class:"user-details"},st={class:"user-name"},ot={class:"user-meta"},nt={class:"score-text"},rt=xe({__name:"UserStats",setup(it){const H=Oe(),z=i(!1),T=i(!1),E=i("30days"),D=i(!1),V=i("daily"),P=i(),B=i(),L=i(),M=i(),O=i();let p=null,_=null,v=null,g=null,y=null;const J=i([{key:"totalUsers",label:"总用户数",value:12580,trend:8.5,icon:Ue,bgColor:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",iconColor:"#fff"},{key:"activeUsers",label:"活跃用户",value:3420,trend:12.3,icon:Se,bgColor:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",iconColor:"#fff"},{key:"newUsers",label:"新增用户",value:158,trend:-2.1,icon:De,bgColor:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",iconColor:"#fff"},{key:"avgSessionTime",label:"平均会话时长",value:25,trend:5.8,icon:Re,bgColor:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",iconColor:"#fff"}]),Y=i([{action:"登录",count:15420,users:3420,avgPerUser:4.5},{action:"浏览食谱",count:35680,users:2890,avgPerUser:12.3},{action:"发布食谱",count:2150,users:680,avgPerUser:3.2},{action:"点赞",count:8960,users:1850,avgPerUser:4.8},{action:"评论",count:3420,users:920,avgPerUser:3.7},{action:"收藏",count:5680,users:1420,avgPerUser:4}]),ee=i([{period:"本周",newUsers:580,day1:68,day7:45,day30:25},{period:"上周",newUsers:620,day1:72,day7:48,day30:28},{period:"上上周",newUsers:545,day1:65,day7:42,day30:22},{period:"3周前",newUsers:680,day1:75,day7:52,day30:30},{period:"4周前",newUsers:720,day1:78,day7:55,day30:32}]),te=i([{id:1,username:"chef001",nickname:"美食大师",avatar:"",registrationTime:"2024-01-10",activityScore:95,publishCount:68,interactionCount:1250,loginDays:28},{id:2,username:"cooker_pro",nickname:"料理专家",avatar:"",registrationTime:"2024-01-05",activityScore:88,publishCount:45,interactionCount:980,loginDays:25},{id:3,username:"food_lover",nickname:"美食爱好者",avatar:"",registrationTime:"2024-01-15",activityScore:82,publishCount:32,interactionCount:760,loginDays:22}]);Ae(()=>{N(),ze(()=>{oe()})}),Te(()=>{p==null||p.dispose(),_==null||_.dispose(),v==null||v.dispose(),g==null||g.dispose(),y==null||y.dispose()});const N=async()=>{z.value=!0;try{await new Promise(o=>setTimeout(o,1e3))}catch{h.error("加载数据失败")}finally{z.value=!1}},ae=()=>{N(),I()},le=()=>{N(),I()},se=async()=>{T.value=!0;try{await new Promise(o=>setTimeout(o,2e3)),h.success("数据导出成功")}catch{h.error("导出失败")}finally{T.value=!1}},oe=()=>{ne(),re(),ie(),de(),ue()},I=()=>{F(),W(),X(),K(),Q()},ne=()=>{P.value&&(p=S(P.value),F())},F=()=>{if(!p)return;const o={tooltip:{trigger:"axis"},legend:{data:D.value?["累积用户"]:["新增用户","活跃用户"]},grid:{left:10,right:10,top:50,bottom:20,containLabel:!0},xAxis:{type:"category",data:["01-14","01-15","01-16","01-17","01-18","01-19","01-20"]},yAxis:{type:"value"},series:D.value?[{name:"累积用户",type:"line",smooth:!0,data:[12e3,12150,12280,12420,12520,12650,12580],lineStyle:{color:"#409eff"},areaStyle:{color:new q(0,0,0,1,[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}])}}]:[{name:"新增用户",type:"bar",data:[120,150,180,220,200,250,158],itemStyle:{color:"#409eff"}},{name:"活跃用户",type:"line",smooth:!0,data:[2800,2950,3100,3200,3150,3380,3420],lineStyle:{color:"#67c23a"}}]};p.setOption(o)},re=()=>{B.value&&(_=S(B.value),W())},W=()=>{if(!_)return;const o={tooltip:{trigger:"axis"},legend:{data:["DAU","WAU","MAU"]},grid:{left:10,right:10,top:50,bottom:20,containLabel:!0},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{name:"DAU",type:"line",data:[820,932,901,934,1290,1330,1320],lineStyle:{color:"#409eff"}},{name:"WAU",type:"line",data:[2800,2900,2850,2950,3100,3200,3180],lineStyle:{color:"#67c23a"}},{name:"MAU",type:"line",data:[8500,8600,8550,8700,8800,8900,8850],lineStyle:{color:"#e6a23c"}}]};_.setOption(o)},ie=()=>{L.value&&(v=S(L.value),X())},X=()=>{if(!v)return;const o={tooltip:{trigger:"item"},series:[{type:"pie",radius:"70%",data:[{value:3580,name:"北京"},{value:2890,name:"上海"},{value:2450,name:"广州"},{value:1980,name:"深圳"},{value:1670,name:"其他"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};v.setOption(o)},de=()=>{M.value&&(g=S(M.value),K())},K=()=>{if(!g)return;const o={tooltip:{trigger:"axis"},grid:{left:10,right:10,top:20,bottom:20,containLabel:!0},xAxis:{type:"category",data:["18-25","26-30","31-35","36-40","41-45","46+"]},yAxis:{type:"value"},series:[{type:"bar",data:[2580,3420,2890,1980,1250,460],itemStyle:{color:new q(0,0,0,1,[{offset:0,color:"#83bff6"},{offset:.5,color:"#188df0"},{offset:1,color:"#188df0"}])}}]};g.setOption(o)},ue=()=>{O.value&&(y=S(O.value),Q())},Q=()=>{if(!y)return;const o={tooltip:{trigger:"item"},series:[{type:"pie",radius:["40%","70%"],data:[{value:6580,name:"移动端"},{value:4200,name:"PC端"},{value:1800,name:"平板"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};y.setOption(o)},ce=()=>{console.log("更新排行榜:",V.value)},fe=o=>{H.push(`/admin/users/${o}`)},pe=o=>{h.info(`向用户 ${o.nickname} 发送消息`)},_e=()=>{h.info("查看用户行为详情")},ve=()=>{h.info("查看用户留存详情")},R=o=>o>=1e6?(o/1e6).toFixed(1)+"M":o>=1e3?(o/1e3).toFixed(1)+"K":o.toString(),ge=o=>new Date(o).toLocaleDateString("zh-CN"),G=o=>o>=50?"retention-high":o>=30?"retention-medium":"retention-low",ye=o=>o>=80?"#67c23a":o>=60?"#e6a23c":"#f56c6c";return(o,l)=>{const w=d("el-option"),me=d("el-select"),k=d("el-icon"),m=d("el-button"),c=d("el-card"),f=d("el-col"),x=d("el-row"),he=d("el-switch"),n=d("el-table-column"),$=d("el-table"),j=d("el-radio-button"),be=d("el-radio-group"),Ce=d("el-avatar"),we=d("el-progress");return C(),Z("div",Ee,[s("div",Ie,[l[5]||(l[5]=s("h2",null,"用户分析",-1)),l[6]||(l[6]=s("p",null,"深度分析用户行为和趋势数据",-1)),s("div",We,[e(me,{modelValue:E.value,"onUpdate:modelValue":l[0]||(l[0]=a=>E.value=a),placeholder:"选择时间范围",onChange:le},{default:t(()=>[e(w,{label:"最近7天",value:"7days"}),e(w,{label:"最近30天",value:"30days"}),e(w,{label:"最近90天",value:"90days"}),e(w,{label:"最近半年",value:"6months"}),e(w,{label:"最近一年",value:"1year"})]),_:1},8,["modelValue"]),e(m,{onClick:se,loading:T.value},{default:t(()=>[e(k,null,{default:t(()=>[e(U(Ve))]),_:1}),l[3]||(l[3]=u(" 导出数据 "))]),_:1,__:[3]},8,["loading"]),e(m,{type:"primary",onClick:ae,loading:z.value},{default:t(()=>[e(k,null,{default:t(()=>[e(U(Pe))]),_:1}),l[4]||(l[4]=u(" 刷新 "))]),_:1,__:[4]},8,["loading"])])]),e(x,{gutter:20,class:"metrics-section"},{default:t(()=>[(C(!0),Z(Be,null,Le(J.value,a=>(C(),A(f,{xs:24,sm:12,lg:6,key:a.key},{default:t(()=>[e(c,{class:"metric-card",shadow:"hover"},{default:t(()=>[s("div",Xe,[s("div",{class:"metric-icon",style:Ne({background:a.bgColor})},[e(k,{size:28,color:a.iconColor},{default:t(()=>[(C(),A(Fe(a.icon)))]),_:2},1032,["color"])],4),s("div",Ke,[s("div",Qe,r(R(a.value)),1),s("div",Ze,r(a.label),1),s("div",qe,[e(k,{class:b(a.trend>0?"trend-up":"trend-down")},{default:t(()=>[a.trend>0?(C(),A(U(Ge),{key:0})):(C(),A(U($e),{key:1}))]),_:2},1032,["class"]),s("span",{class:b(a.trend>0?"trend-up":"trend-down")},r(a.trend>0?"+":"")+r(a.trend)+"% ",3)])])])]),_:2},1024)]),_:2},1024))),128))]),_:1}),e(x,{gutter:20,class:"charts-section"},{default:t(()=>[e(f,{xs:24,lg:12},{default:t(()=>[e(c,{class:"chart-card"},{header:t(()=>[s("div",He,[l[7]||(l[7]=s("span",null,"用户增长趋势",-1)),e(he,{modelValue:D.value,"onUpdate:modelValue":l[1]||(l[1]=a=>D.value=a),"active-text":"累积","inactive-text":"新增",onChange:F},null,8,["modelValue"])])]),default:t(()=>[s("div",{class:"chart-container",ref_key:"growthChartRef",ref:P},null,512)]),_:1})]),_:1}),e(f,{xs:24,lg:12},{default:t(()=>[e(c,{class:"chart-card"},{header:t(()=>l[8]||(l[8]=[s("span",null,"用户活跃度分析",-1)])),default:t(()=>[s("div",{class:"chart-container",ref_key:"activityChartRef",ref:B},null,512)]),_:1})]),_:1})]),_:1}),e(x,{gutter:20,class:"charts-section"},{default:t(()=>[e(f,{xs:24,lg:8},{default:t(()=>[e(c,{class:"chart-card"},{header:t(()=>l[9]||(l[9]=[s("span",null,"用户地域分布",-1)])),default:t(()=>[s("div",{class:"chart-container",ref_key:"regionChartRef",ref:L},null,512)]),_:1})]),_:1}),e(f,{xs:24,lg:8},{default:t(()=>[e(c,{class:"chart-card"},{header:t(()=>l[10]||(l[10]=[s("span",null,"用户年龄分布",-1)])),default:t(()=>[s("div",{class:"chart-container",ref_key:"ageChartRef",ref:M},null,512)]),_:1})]),_:1}),e(f,{xs:24,lg:8},{default:t(()=>[e(c,{class:"chart-card"},{header:t(()=>l[11]||(l[11]=[s("span",null,"设备类型分析",-1)])),default:t(()=>[s("div",{class:"chart-container",ref_key:"deviceChartRef",ref:O},null,512)]),_:1})]),_:1})]),_:1}),e(x,{gutter:20,class:"table-section"},{default:t(()=>[e(f,{xs:24,lg:12},{default:t(()=>[e(c,{class:"table-card"},{header:t(()=>[s("div",Je,[l[13]||(l[13]=s("span",null,"用户行为分析",-1)),e(m,{size:"small",onClick:_e},{default:t(()=>l[12]||(l[12]=[u("查看详情")])),_:1,__:[12]})])]),default:t(()=>[e($,{data:Y.value,size:"small",height:"300"},{default:t(()=>[e(n,{prop:"action",label:"行为类型",width:"120"}),e(n,{prop:"count",label:"次数",width:"80"},{default:t(({row:a})=>[u(r(R(a.count)),1)]),_:1}),e(n,{prop:"users",label:"用户数",width:"80"},{default:t(({row:a})=>[u(r(R(a.users)),1)]),_:1}),e(n,{prop:"avgPerUser",label:"人均次数"},{default:t(({row:a})=>[u(r((a.count/a.users).toFixed(1)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),e(f,{xs:24,lg:12},{default:t(()=>[e(c,{class:"table-card"},{header:t(()=>[s("div",Ye,[l[15]||(l[15]=s("span",null,"用户留存率",-1)),e(m,{size:"small",onClick:ve},{default:t(()=>l[14]||(l[14]=[u("查看详情")])),_:1,__:[14]})])]),default:t(()=>[e($,{data:ee.value,size:"small",height:"300"},{default:t(()=>[e(n,{prop:"period",label:"时间周期",width:"100"}),e(n,{prop:"newUsers",label:"新用户数",width:"100"},{default:t(({row:a})=>[u(r(R(a.newUsers)),1)]),_:1}),e(n,{prop:"day1",label:"次日留存"},{default:t(({row:a})=>[s("span",{class:b(G(a.day1))},r(a.day1)+"%",3)]),_:1}),e(n,{prop:"day7",label:"7日留存"},{default:t(({row:a})=>[s("span",{class:b(G(a.day7))},r(a.day7)+"%",3)]),_:1}),e(n,{prop:"day30",label:"30日留存"},{default:t(({row:a})=>[s("span",{class:b(G(a.day30))},r(a.day30)+"%",3)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1}),e(x,{gutter:20,class:"ranking-section"},{default:t(()=>[e(f,{span:24},{default:t(()=>[e(c,{class:"ranking-card"},{header:t(()=>[s("div",et,[l[19]||(l[19]=s("span",null,"活跃用户排行榜",-1)),s("div",tt,[e(be,{modelValue:V.value,"onUpdate:modelValue":l[2]||(l[2]=a=>V.value=a),size:"small",onChange:ce},{default:t(()=>[e(j,{label:"daily"},{default:t(()=>l[16]||(l[16]=[u("日活跃")])),_:1,__:[16]}),e(j,{label:"weekly"},{default:t(()=>l[17]||(l[17]=[u("周活跃")])),_:1,__:[17]}),e(j,{label:"monthly"},{default:t(()=>l[18]||(l[18]=[u("月活跃")])),_:1,__:[18]})]),_:1},8,["modelValue"])])])]),default:t(()=>[e($,{data:te.value,size:"small"},{default:t(()=>[e(n,{label:"排名",width:"80"},{default:t(({$index:a})=>[s("div",{class:b(["rank-badge",`rank-${a+1}`])},r(a+1),3)]),_:1}),e(n,{label:"用户信息","min-width":"200"},{default:t(({row:a})=>[s("div",at,[e(Ce,{size:32,src:a.avatar},{default:t(()=>[e(k,null,{default:t(()=>[e(U(Me))]),_:1})]),_:2},1032,["src"]),s("div",lt,[s("div",st,r(a.nickname||a.username),1),s("div",ot,"注册于 "+r(ge(a.registrationTime)),1)])])]),_:1}),e(n,{prop:"activityScore",label:"活跃度评分",width:"120"},{default:t(({row:a})=>[e(we,{percentage:a.activityScore,"stroke-width":8,"show-text":!1,color:ye(a.activityScore)},null,8,["percentage","color"]),s("span",nt,r(a.activityScore),1)]),_:1}),e(n,{prop:"publishCount",label:"发布内容",width:"100"}),e(n,{prop:"interactionCount",label:"互动次数",width:"100"}),e(n,{prop:"loginDays",label:"活跃天数",width:"100"}),e(n,{label:"操作",width:"120"},{default:t(({row:a})=>[e(m,{type:"text",size:"small",onClick:ke=>fe(a.id)},{default:t(()=>l[20]||(l[20]=[u(" 查看详情 ")])),_:2,__:[20]},1032,["onClick"]),e(m,{type:"text",size:"small",onClick:ke=>pe(a)},{default:t(()=>l[21]||(l[21]=[u(" 发消息 ")])),_:2,__:[21]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1})])}}}),ft=je(rt,[["__scopeId","data-v-5f0b015b"]]);export{ft as default};
