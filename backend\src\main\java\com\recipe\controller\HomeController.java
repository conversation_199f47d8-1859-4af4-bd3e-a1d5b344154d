package com.recipe.controller;

import com.recipe.common.Result;
import com.recipe.dto.HomeStatsDTO;
import com.recipe.dto.LatestRecipeDTO;
import com.recipe.dto.PopularCreatorDTO;
import com.recipe.service.IHomeService;
import com.recipe.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 首页控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/home")
@Tag(name = "首页管理", description = "首页相关接口")
public class HomeController {

    @Autowired
    private IHomeService homeService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取首页统计数据
     */
    @GetMapping("/stats")
    @Operation(summary = "获取首页统计数据", description = "获取全站统计数据和用户个人统计数据")
    public Result<HomeStatsDTO> getHomeStats(HttpServletRequest request) {
        try {
            // 尝试获取当前用户ID（可能为空）
            Long userId = null;
            try {
                String token = jwtUtil.getTokenFromRequest(request);
                if (token != null) {
                    userId = jwtUtil.getUserIdFromToken(token);
                }
            } catch (Exception e) {
                // 忽略token解析错误，继续以未登录状态处理
                log.debug("Token解析失败，以未登录状态处理: {}", e.getMessage());
            }

            HomeStatsDTO stats = homeService.getHomeStats(userId);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取首页统计数据失败", e);
            return Result.error("获取统计数据失败");
        }
    }

    /**
     * 获取最新发布的食谱
     */
    @GetMapping("/latest-recipes")
    @Operation(summary = "获取最新食谱", description = "获取最新发布的食谱列表")
    public Result<List<LatestRecipeDTO>> getLatestRecipes(
            @Parameter(description = "限制数量，默认8条") @RequestParam(defaultValue = "8") Integer limit) {
        try {
            List<LatestRecipeDTO> recipes = homeService.getLatestRecipes(limit);
            return Result.success(recipes);
        } catch (Exception e) {
            log.error("获取最新食谱失败", e);
            return Result.error("获取最新食谱失败");
        }
    }

    /**
     * 获取热门创作者
     */
    @GetMapping("/popular-creators")
    @Operation(summary = "获取热门创作者", description = "获取热门创作者列表")
    public Result<List<PopularCreatorDTO>> getPopularCreators(
            @Parameter(description = "限制数量，默认6条") @RequestParam(defaultValue = "6") Integer limit,
            HttpServletRequest request) {
        try {
            // 尝试获取当前用户ID（用于判断关注状态）
            Long userId = null;
            try {
                String token = jwtUtil.getTokenFromRequest(request);
                if (token != null) {
                    userId = jwtUtil.getUserIdFromToken(token);
                }
            } catch (Exception e) {
                // 忽略token解析错误
                log.debug("Token解析失败，以未登录状态处理: {}", e.getMessage());
            }

            List<PopularCreatorDTO> creators = homeService.getPopularCreators(userId, limit);
            return Result.success(creators);
        } catch (Exception e) {
            log.error("获取热门创作者失败", e);
            return Result.error("获取热门创作者失败");
        }
    }

    /**
     * 获取推荐食谱
     */
    @GetMapping("/recommended-recipes")
    @Operation(summary = "获取推荐食谱", description = "基于用户喜好获取推荐食谱")
    public Result<List<LatestRecipeDTO>> getRecommendedRecipes(
            @Parameter(description = "限制数量，默认8条") @RequestParam(defaultValue = "8") Integer limit,
            HttpServletRequest request) {
        try {
            // 尝试获取当前用户ID
            Long userId = null;
            try {
                String token = jwtUtil.getTokenFromRequest(request);
                if (token != null) {
                    userId = jwtUtil.getUserIdFromToken(token);
                }
            } catch (Exception e) {
                // 忽略token解析错误
                log.debug("Token解析失败，以未登录状态处理: {}", e.getMessage());
            }

            List<LatestRecipeDTO> recipes = homeService.getRecommendedRecipes(userId, limit);
            return Result.success(recipes);
        } catch (Exception e) {
            log.error("获取推荐食谱失败", e);
            return Result.error("获取推荐食谱失败");
        }
    }

    /**
     * 获取热门搜索关键词
     */
    @GetMapping("/hot-keywords")
    @Operation(summary = "获取热门搜索关键词", description = "获取当前热门搜索关键词")
    public Result<List<String>> getHotSearchKeywords(
            @Parameter(description = "限制数量，默认10条") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<String> keywords = homeService.getHotSearchKeywords(limit);
            return Result.success(keywords);
        } catch (Exception e) {
            log.error("获取热门搜索关键词失败", e);
            return Result.error("获取热门搜索关键词失败");
        }
    }
}
