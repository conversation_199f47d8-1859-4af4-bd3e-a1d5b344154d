import{d as Re,r as b,a as E,o as Ne,p as v,c as j,e as s,f as e,w as l,j as u,n as i,h as _,F as Se,g as He,H as $e,I as je,t as r,J as D,D as Fe,U as qe,s as Be,P as L,af as K,G as le,a1 as Ie,N as ae,C as U,k as T}from"./index-BDkHONMN.js";import{_ as Oe}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ee={class:"announcement-page"},Le={class:"page-header"},Ke={class:"header-actions"},Ge={class:"table-header"},Je={class:"table-actions"},We={class:"announcement-info"},Qe={class:"announcement-title"},Xe={class:"announcement-meta"},Ze={class:"meta-item"},et={class:"meta-item"},tt={class:"time-range"},lt={key:0},at={class:"pagination-wrapper"},nt={class:"dialog-footer"},ot={class:"announcement-detail"},st={class:"detail-meta"},it={class:"publish-info"},ut={class:"detail-content"},dt={key:0,class:"detail-stats"},rt={class:"stat-item"},mt={class:"stat-value"},pt={class:"stat-item"},ct={class:"stat-value"},ft={key:0,class:"stat-item"},_t={class:"stat-value"},vt=Re({__name:"AnnouncementList",setup(gt){const P=b(!1),k=b(!1),w=b(!1),F=b(!1),M=b(!1),R=b(),g=E({title:"",type:"",status:"",dateRange:[]}),V=E({page:1,size:20,total:0}),G=b([{id:1,title:"系统维护通知",content:"为了提供更好的服务体验，系统将于本周末进行例行维护...",type:"maintenance",status:"published",priority:"high",publisher:"系统管理员",publishTime:"2024-01-20 10:00:00",effectiveTime:"2024-01-21 02:00:00",expireTime:"2024-01-21 06:00:00",pinned:!0,viewCount:1250,sendNotification:!0,forceRead:!0,targetAudience:"all",attachments:[]},{id:2,title:"新功能上线公告",content:"我们很高兴地宣布，食谱分享功能现已正式上线...",type:"update",status:"published",priority:"medium",publisher:"产品经理",publishTime:"2024-01-19 15:30:00",effectiveTime:"2024-01-19 15:30:00",pinned:!1,viewCount:890,sendNotification:!0,forceRead:!1,targetAudience:"registered",attachments:[]}]),x=b([]),f=b({}),o=E({id:null,title:"",content:"",type:"system",priority:"medium",effectiveTime:"",expireTime:"",pinned:!1,sendNotification:!0,forceRead:!1,targetAudience:"all",attachments:[]}),ne={title:[{required:!0,message:"请输入公告标题",trigger:"blur"},{min:5,max:100,message:"标题长度在 5 到 100 个字符",trigger:"blur"}],content:[{required:!0,message:"请输入公告内容",trigger:"blur"},{min:10,message:"内容至少10个字符",trigger:"blur"}],type:[{required:!0,message:"请选择公告类型",trigger:"change"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}],effectiveTime:[{required:!0,message:"请选择生效时间",trigger:"change"}]};Ne(()=>{y()});const y=async()=>{P.value=!0;try{await new Promise(n=>setTimeout(n,1e3)),V.total=G.value.length}catch{v.error("加载公告列表失败")}finally{P.value=!1}},q=()=>{V.page=1,y()},oe=()=>{Object.assign(g,{title:"",type:"",status:"",dateRange:[]}),q()},se=()=>{y()},ie=()=>{y()},ue=n=>{x.value=n},de=()=>{M.value=!1,pe(),w.value=!0},re=n=>{M.value=!0,Object.assign(o,n),w.value=!0},me=n=>{f.value=n,F.value=!0},pe=()=>{var n;Object.assign(o,{id:null,title:"",content:"",type:"system",priority:"medium",effectiveTime:"",expireTime:"",pinned:!1,sendNotification:!0,forceRead:!1,targetAudience:"all",attachments:[]}),(n=R.value)==null||n.clearValidate()},ce=async()=>{try{await R.value.validate(),k.value=!0,await new Promise(n=>setTimeout(n,1e3)),v.success(M.value?"公告更新成功":"公告发布成功"),w.value=!1,y()}catch(n){console.error("表单验证失败:",n)}finally{k.value=!1}},fe=async()=>{try{await R.value.validate(["title","content"]),k.value=!0,await new Promise(n=>setTimeout(n,1e3)),v.success("草稿保存成功"),w.value=!1,y()}catch(n){console.error("保存草稿失败:",n)}finally{k.value=!1}},_e=async n=>{try{await new Promise(t=>setTimeout(t,500)),v.success(n.pinned?"已置顶":"已取消置顶")}catch{n.pinned=!n.pinned,v.error("操作失败")}},ve=async(n,t)=>{switch(n){case"publish":await ge(t);break;case"offline":await ye(t);break;case"copy":be(t);break;case"delete":await we();break}},ge=async n=>{try{await U.confirm("确定要发布这条公告吗？","确认发布",{type:"warning"}),await new Promise(t=>setTimeout(t,1e3)),n.status="published",v.success("公告发布成功")}catch{}},ye=async n=>{try{await U.confirm("确定要下线这条公告吗？","确认下线",{type:"warning"}),await new Promise(t=>setTimeout(t,1e3)),n.status="offline",v.success("公告已下线")}catch{}},be=n=>{const t={...n};t.title=t.title+" (副本)";const{id:d,...p}=t;Object.assign(o,p),M.value=!1,w.value=!0},we=async n=>{try{await U.confirm("确定要删除这条公告吗？此操作不可逆！","确认删除",{type:"error"}),await new Promise(t=>setTimeout(t,1e3)),v.success("公告删除成功"),y()}catch{}},he=(n,t)=>{o.attachments.push({name:t.name,url:n.url})},Ve=n=>{const t=o.attachments.findIndex(d=>d.name===n.name);t>-1&&o.attachments.splice(t,1)},z=n=>n?new Date(n).toLocaleString("zh-CN"):"",J=n=>({system:"",update:"success",maintenance:"warning",activity:"info"})[n]||"",W=n=>({system:"系统通知",update:"功能更新",maintenance:"维护公告",activity:"活动公告"})[n]||n||"",xe=n=>({published:"success",draft:"info",offline:"danger"})[n]||"",Ce=n=>({published:"已发布",draft:"草稿",offline:"已下线"})[n]||n,Q=n=>({low:"info",medium:"",high:"warning",urgent:"danger"})[n]||"",X=n=>({low:"低",medium:"中",high:"高",urgent:"紧急"})[n]||n||"",Te=async()=>{try{await U.confirm(`确定要批量发布选中的 ${x.value.length} 条公告吗？`,"批量发布",{type:"warning"}),await new Promise(n=>setTimeout(n,1e3)),v.success("批量发布成功"),y()}catch{}},ke=async()=>{try{await U.confirm(`确定要批量下线选中的 ${x.value.length} 条公告吗？`,"批量下线",{type:"warning"}),await new Promise(n=>setTimeout(n,1e3)),v.success("批量下线成功"),y()}catch{}},Me=async()=>{try{await U.confirm(`确定要批量删除选中的 ${x.value.length} 条公告吗？此操作不可逆！`,"批量删除",{type:"error"}),await new Promise(n=>setTimeout(n,1e3)),v.success("批量删除成功"),y()}catch{}};return(n,t)=>{const d=u("el-icon"),p=u("el-button"),B=u("el-input"),m=u("el-form-item"),c=u("el-option"),N=u("el-select"),I=u("el-date-picker"),Z=u("el-form"),ee=u("el-card"),h=u("el-table-column"),A=u("el-tag"),S=u("el-switch"),H=u("el-dropdown-item"),ze=u("el-dropdown-menu"),Ye=u("el-dropdown"),De=u("el-table"),Ue=u("el-pagination"),C=u("el-col"),O=u("el-row"),$=u("el-radio"),Ae=u("el-radio-group"),Pe=u("el-upload"),te=u("el-dialog");return T(),j("div",Ee,[s("div",Le,[t[20]||(t[20]=s("h2",null,"公告管理",-1)),t[21]||(t[21]=s("p",null,"管理系统公告和通知信息",-1)),s("div",Ke,[e(p,{type:"primary",onClick:de},{default:l(()=>[e(d,null,{default:l(()=>[e(_(Se))]),_:1}),t[19]||(t[19]=i(" 发布公告 "))]),_:1,__:[19]})])]),e(ee,{class:"search-card"},{default:l(()=>[e(Z,{model:g,inline:!0,class:"search-form"},{default:l(()=>[e(m,{label:"公告标题"},{default:l(()=>[e(B,{modelValue:g.title,"onUpdate:modelValue":t[0]||(t[0]=a=>g.title=a),placeholder:"搜索公告标题",clearable:"",onKeyup:He(q,["enter"])},null,8,["modelValue"])]),_:1}),e(m,{label:"公告类型"},{default:l(()=>[e(N,{modelValue:g.type,"onUpdate:modelValue":t[1]||(t[1]=a=>g.type=a),placeholder:"选择类型",clearable:""},{default:l(()=>[e(c,{label:"系统通知",value:"system"}),e(c,{label:"功能更新",value:"update"}),e(c,{label:"维护公告",value:"maintenance"}),e(c,{label:"活动公告",value:"activity"})]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"发布状态"},{default:l(()=>[e(N,{modelValue:g.status,"onUpdate:modelValue":t[2]||(t[2]=a=>g.status=a),placeholder:"选择状态",clearable:""},{default:l(()=>[e(c,{label:"已发布",value:"published"}),e(c,{label:"草稿",value:"draft"}),e(c,{label:"已下线",value:"offline"})]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"发布时间"},{default:l(()=>[e(I,{modelValue:g.dateRange,"onUpdate:modelValue":t[3]||(t[3]=a=>g.dateRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(m,null,{default:l(()=>[e(p,{type:"primary",onClick:q,loading:P.value},{default:l(()=>[e(d,null,{default:l(()=>[e(_($e))]),_:1}),t[22]||(t[22]=i(" 搜索 "))]),_:1,__:[22]},8,["loading"]),e(p,{onClick:oe},{default:l(()=>[e(d,null,{default:l(()=>[e(_(je))]),_:1}),t[23]||(t[23]=i(" 重置 "))]),_:1,__:[23]})]),_:1})]),_:1},8,["model"])]),_:1}),e(ee,{class:"table-card"},{header:l(()=>[s("div",Ge,[t[27]||(t[27]=s("span",null,"公告列表",-1)),s("div",Je,[e(p,{size:"small",onClick:Te,disabled:!x.value.length},{default:l(()=>[e(d,null,{default:l(()=>[e(_(K))]),_:1}),t[24]||(t[24]=i(" 批量发布 "))]),_:1,__:[24]},8,["disabled"]),e(p,{size:"small",onClick:ke,disabled:!x.value.length},{default:l(()=>[e(d,null,{default:l(()=>[e(_(le))]),_:1}),t[25]||(t[25]=i(" 批量下线 "))]),_:1,__:[25]},8,["disabled"]),e(p,{size:"small",type:"danger",onClick:Me,disabled:!x.value.length},{default:l(()=>[e(d,null,{default:l(()=>[e(_(ae))]),_:1}),t[26]||(t[26]=i(" 批量删除 "))]),_:1,__:[26]},8,["disabled"])])])]),default:l(()=>[e(De,{data:G.value,loading:P.value,onSelectionChange:ue,stripe:""},{default:l(()=>[e(h,{type:"selection",width:"55"}),e(h,{label:"公告信息","min-width":"300"},{default:l(({row:a})=>[s("div",We,[s("div",Qe,r(a.title),1),s("div",Xe,[e(A,{type:J(a.type),size:"small"},{default:l(()=>[i(r(W(a.type)),1)]),_:2},1032,["type"]),s("span",Ze,"发布者: "+r(a.publisher),1),s("span",et,r(z(a.publishTime)),1)])])]),_:1}),e(h,{label:"状态",width:"100"},{default:l(({row:a})=>[e(A,{type:xe(a.status),size:"small"},{default:l(()=>[i(r(Ce(a.status)),1)]),_:2},1032,["type"])]),_:1}),e(h,{label:"优先级",width:"100"},{default:l(({row:a})=>[e(A,{type:Q(a.priority),size:"small"},{default:l(()=>[i(r(X(a.priority)),1)]),_:2},1032,["type"])]),_:1}),e(h,{prop:"viewCount",label:"浏览量",width:"100"}),e(h,{label:"置顶",width:"80"},{default:l(({row:a})=>[e(S,{modelValue:a.pinned,"onUpdate:modelValue":Y=>a.pinned=Y,onChange:Y=>_e(a),disabled:a.status!=="published"},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),e(h,{label:"生效时间",width:"160"},{default:l(({row:a})=>[s("div",tt,[s("div",null,r(z(a.effectiveTime)),1),a.expireTime?(T(),j("div",lt,"至 "+r(z(a.expireTime)),1)):D("",!0)])]),_:1}),e(h,{label:"操作",width:"200",fixed:"right"},{default:l(({row:a})=>[e(p,{type:"text",size:"small",onClick:Y=>me(a)},{default:l(()=>[e(d,null,{default:l(()=>[e(_(Fe))]),_:1}),t[28]||(t[28]=i(" 查看 "))]),_:2,__:[28]},1032,["onClick"]),e(p,{type:"text",size:"small",onClick:Y=>re(a)},{default:l(()=>[e(d,null,{default:l(()=>[e(_(qe))]),_:1}),t[29]||(t[29]=i(" 编辑 "))]),_:2,__:[29]},1032,["onClick"]),e(Ye,{onCommand:Y=>ve(Y,a)},{dropdown:l(()=>[e(ze,null,{default:l(()=>[a.status==="draft"?(T(),L(H,{key:0,command:"publish"},{default:l(()=>[e(d,null,{default:l(()=>[e(_(K))]),_:1}),t[31]||(t[31]=i(" 发布 "))]),_:1,__:[31]})):D("",!0),a.status==="published"?(T(),L(H,{key:1,command:"offline"},{default:l(()=>[e(d,null,{default:l(()=>[e(_(le))]),_:1}),t[32]||(t[32]=i(" 下线 "))]),_:1,__:[32]})):D("",!0),e(H,{command:"copy"},{default:l(()=>[e(d,null,{default:l(()=>[e(_(Ie))]),_:1}),t[33]||(t[33]=i(" 复制 "))]),_:1,__:[33]}),e(H,{command:"delete",divided:""},{default:l(()=>[e(d,null,{default:l(()=>[e(_(ae))]),_:1}),t[34]||(t[34]=i(" 删除 "))]),_:1,__:[34]})]),_:2},1024)]),default:l(()=>[e(p,{type:"text",size:"small"},{default:l(()=>[t[30]||(t[30]=i(" 更多")),e(d,null,{default:l(()=>[e(_(Be))]),_:1})]),_:1,__:[30]})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data","loading"]),s("div",at,[e(Ue,{"current-page":V.page,"onUpdate:currentPage":t[4]||(t[4]=a=>V.page=a),"page-size":V.size,"onUpdate:pageSize":t[5]||(t[5]=a=>V.size=a),total:V.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:se,onCurrentChange:ie},null,8,["current-page","page-size","total"])])]),_:1}),e(te,{modelValue:w.value,"onUpdate:modelValue":t[17]||(t[17]=a=>w.value=a),title:M.value?"编辑公告":"发布新公告",width:"800px","close-on-click-modal":!1},{footer:l(()=>[s("div",nt,[e(p,{onClick:t[16]||(t[16]=a=>w.value=!1)},{default:l(()=>t[40]||(t[40]=[i("取消")])),_:1,__:[40]}),e(p,{onClick:fe,loading:k.value},{default:l(()=>t[41]||(t[41]=[i("保存草稿")])),_:1,__:[41]},8,["loading"]),e(p,{type:"primary",onClick:ce,loading:k.value},{default:l(()=>[i(r(M.value?"更新公告":"立即发布"),1)]),_:1},8,["loading"])])]),default:l(()=>[e(Z,{ref_key:"formRef",ref:R,model:o,rules:ne,"label-width":"100px",size:"default"},{default:l(()=>[e(m,{label:"公告标题",prop:"title"},{default:l(()=>[e(B,{modelValue:o.title,"onUpdate:modelValue":t[6]||(t[6]=a=>o.title=a),placeholder:"请输入公告标题"},null,8,["modelValue"])]),_:1}),e(O,{gutter:20},{default:l(()=>[e(C,{span:12},{default:l(()=>[e(m,{label:"公告类型",prop:"type"},{default:l(()=>[e(N,{modelValue:o.type,"onUpdate:modelValue":t[7]||(t[7]=a=>o.type=a),style:{width:"100%"}},{default:l(()=>[e(c,{label:"系统通知",value:"system"}),e(c,{label:"功能更新",value:"update"}),e(c,{label:"维护公告",value:"maintenance"}),e(c,{label:"活动公告",value:"activity"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(C,{span:12},{default:l(()=>[e(m,{label:"优先级",prop:"priority"},{default:l(()=>[e(N,{modelValue:o.priority,"onUpdate:modelValue":t[8]||(t[8]=a=>o.priority=a),style:{width:"100%"}},{default:l(()=>[e(c,{label:"低",value:"low"}),e(c,{label:"中",value:"medium"}),e(c,{label:"高",value:"high"}),e(c,{label:"紧急",value:"urgent"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{label:"公告内容",prop:"content"},{default:l(()=>[e(B,{modelValue:o.content,"onUpdate:modelValue":t[9]||(t[9]=a=>o.content=a),type:"textarea",rows:6,placeholder:"请输入公告内容"},null,8,["modelValue"])]),_:1}),e(O,{gutter:20},{default:l(()=>[e(C,{span:12},{default:l(()=>[e(m,{label:"生效时间",prop:"effectiveTime"},{default:l(()=>[e(I,{modelValue:o.effectiveTime,"onUpdate:modelValue":t[10]||(t[10]=a=>o.effectiveTime=a),type:"datetime",placeholder:"选择生效时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(C,{span:12},{default:l(()=>[e(m,{label:"过期时间"},{default:l(()=>[e(I,{modelValue:o.expireTime,"onUpdate:modelValue":t[11]||(t[11]=a=>o.expireTime=a),type:"datetime",placeholder:"选择过期时间(可选)",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(O,{gutter:20},{default:l(()=>[e(C,{span:8},{default:l(()=>[e(m,{label:"是否置顶"},{default:l(()=>[e(S,{modelValue:o.pinned,"onUpdate:modelValue":t[12]||(t[12]=a=>o.pinned=a),"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})]),_:1}),e(C,{span:8},{default:l(()=>[e(m,{label:"发送通知"},{default:l(()=>[e(S,{modelValue:o.sendNotification,"onUpdate:modelValue":t[13]||(t[13]=a=>o.sendNotification=a),"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})]),_:1}),e(C,{span:8},{default:l(()=>[e(m,{label:"强制阅读"},{default:l(()=>[e(S,{modelValue:o.forceRead,"onUpdate:modelValue":t[14]||(t[14]=a=>o.forceRead=a),"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{label:"目标用户"},{default:l(()=>[e(Ae,{modelValue:o.targetAudience,"onUpdate:modelValue":t[15]||(t[15]=a=>o.targetAudience=a)},{default:l(()=>[e($,{label:"all"},{default:l(()=>t[35]||(t[35]=[i("所有用户")])),_:1,__:[35]}),e($,{label:"registered"},{default:l(()=>t[36]||(t[36]=[i("注册用户")])),_:1,__:[36]}),e($,{label:"vip"},{default:l(()=>t[37]||(t[37]=[i("VIP用户")])),_:1,__:[37]}),e($,{label:"custom"},{default:l(()=>t[38]||(t[38]=[i("自定义")])),_:1,__:[38]})]),_:1},8,["modelValue"])]),_:1}),o.type!=="system"?(T(),L(m,{key:0,label:"附件"},{default:l(()=>[e(Pe,{"file-list":o.attachments,"on-success":he,"on-remove":Ve,action:"#","auto-upload":!1},{default:l(()=>[e(p,{size:"small"},{default:l(()=>[e(d,null,{default:l(()=>[e(_(K))]),_:1}),t[39]||(t[39]=i(" 上传附件 "))]),_:1,__:[39]})]),_:1},8,["file-list"])]),_:1})):D("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(te,{modelValue:F.value,"onUpdate:modelValue":t[18]||(t[18]=a=>F.value=a),title:f.value.title,width:"600px"},{default:l(()=>[s("div",ot,[s("div",st,[e(A,{type:J(f.value.type),size:"small"},{default:l(()=>[i(r(W(f.value.type)),1)]),_:1},8,["type"]),e(A,{type:Q(f.value.priority),size:"small"},{default:l(()=>[i(r(X(f.value.priority)),1)]),_:1},8,["type"]),s("span",it,r(f.value.publisher)+" 发布于 "+r(z(f.value.publishTime)),1)]),s("div",ut,r(f.value.content),1),f.value.status==="published"?(T(),j("div",dt,[s("div",rt,[t[42]||(t[42]=s("span",{class:"stat-label"},"浏览量:",-1)),s("span",mt,r(f.value.viewCount),1)]),s("div",pt,[t[43]||(t[43]=s("span",{class:"stat-label"},"生效时间:",-1)),s("span",ct,r(z(f.value.effectiveTime)),1)]),f.value.expireTime?(T(),j("div",ft,[t[44]||(t[44]=s("span",{class:"stat-label"},"过期时间:",-1)),s("span",_t,r(z(f.value.expireTime)),1)])):D("",!0)])):D("",!0)])]),_:1},8,["modelValue","title"])])}}}),wt=Oe(vt,[["__scopeId","data-v-40bea312"]]);export{wt as default};
