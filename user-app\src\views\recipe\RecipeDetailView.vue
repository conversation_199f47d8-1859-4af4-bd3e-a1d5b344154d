<template>
  <div class="recipe-detail-view">
    <div class="page-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>

      <!-- 食谱详情内容 -->
      <div v-else-if="recipe" class="recipe-detail">
        <!-- 食谱头部信息 -->
        <div class="recipe-header card">
          <div class="header-content">
            <div class="recipe-info">
              <div class="breadcrumb">
                <el-breadcrumb separator="/">
                  <el-breadcrumb-item @click="router.push('/')">首页</el-breadcrumb-item>
                  <el-breadcrumb-item @click="router.push('/recipes')">食谱列表</el-breadcrumb-item>
                  <el-breadcrumb-item>{{ recipe.title }}</el-breadcrumb-item>
                </el-breadcrumb>
              </div>
              
              <h1 class="recipe-title">{{ recipe.title }}</h1>
              <p class="recipe-description">{{ recipe.description }}</p>
              
              <div class="recipe-meta">
                <div class="meta-item">
                  <el-icon><Timer /></el-icon>
                  <span>总耗时：{{ getTotalTime() }}分钟</span>
                </div>
                <div class="meta-item">
                  <el-icon><User /></el-icon>
                  <span>{{ recipe.servings }}人份</span>
                </div>
                <div class="meta-item">
                  <span class="difficulty" :class="`difficulty-${recipe.difficulty}`">
                    {{ getDifficultyText(recipe.difficulty) }}
                  </span>
                </div>
              </div>

              <div class="recipe-stats">
                <span class="stat-item">
                  <el-icon><View /></el-icon>
                  {{ recipe.views || 0 }}次浏览
                </span>
                <span class="stat-item">
                  <el-icon><Star /></el-icon>
                  {{ recipe.likes || 0 }}次点赞
                </span>
                <span class="stat-item">
                  <el-icon><Collection /></el-icon>
                  {{ recipe.favorites || 0 }}次收藏
                </span>
              </div>

              <div class="recipe-tags" v-if="recipe.tags">
                <el-tag
                  v-for="tag in getTagList(recipe.tags)"
                  :key="tag"
                  class="recipe-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>

            <div class="recipe-image">
              <el-image
                :src="getFileUrl(recipe.coverImage) || '/default-recipe.jpg'"
                :alt="recipe.title"
                fit="cover"
                class="cover-image"
              />
            </div>
          </div>

          <!-- 作者信息 -->
          <div class="author-section">
            <div class="author-info">
              <el-avatar :size="50" :src="recipe.creatorAvatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="author-details">
                <h4>{{ recipe.creatorName }}</h4>
                <p>{{ formatDate(recipe.createdAt) }} 发布</p>
              </div>
            </div>
            
            <div class="action-buttons">
              <el-button
                :type="isLiked ? 'primary' : 'default'"
                @click="toggleLike"
                :loading="likeLoading"
              >
                <el-icon><Star /></el-icon>
                {{ isLiked ? '已点赞' : '点赞' }}
              </el-button>
              
              <el-button
                :type="isFavorited ? 'primary' : 'default'"
                @click="toggleFavorite"
                :loading="favoriteLoading"
              >
                <el-icon><Collection /></el-icon>
                {{ isFavorited ? '已收藏' : '收藏' }}
              </el-button>
              
              <el-button @click="shareRecipe">
                <el-icon><Share /></el-icon>
                分享
              </el-button>
            </div>
          </div>
        </div>

        <!-- 食材和营养信息 -->
        <el-row :gutter="30">
          <el-col :xs="24" :lg="16">
            <!-- 食材清单 -->
            <div class="ingredients-section card">
              <h3 class="section-title">
                <el-icon><Dish /></el-icon>
                食材清单
              </h3>
              <div class="ingredients-list">
                <div
                  v-for="(ingredient, index) in ingredients"
                  :key="index"
                  class="ingredient-item"
                >
                  <span class="ingredient-name">{{ ingredient.name }}</span>
                  <span class="ingredient-amount">{{ ingredient.amount }}</span>
                  <span v-if="ingredient.unit" class="ingredient-unit">{{ ingredient.unit }}</span>
                  <span v-if="ingredient.notes" class="ingredient-notes">{{ ingredient.notes }}</span>
                </div>
              </div>
            </div>

            <!-- 制作步骤 -->
            <div class="instructions-section card">
              <h3 class="section-title">
                <el-icon><Document /></el-icon>
                制作步骤
              </h3>
              <div class="instructions-list">
                <div
                  v-for="(instruction, index) in instructions"
                  :key="index"
                  class="instruction-item"
                >
                  <div class="step-number">{{ index + 1 }}</div>
                  <div class="step-content">
                    <p class="step-description">{{ instruction.description }}</p>
                    <div v-if="instruction.duration" class="step-duration">
                      <el-icon><Timer /></el-icon>
                      {{ instruction.duration }}分钟
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 小贴士 -->
            <div v-if="recipe.tips" class="tips-section card">
              <h3 class="section-title">
                <el-icon><Warning /></el-icon>
                制作小贴士
              </h3>
              <p class="tips-content">{{ recipe.tips }}</p>
            </div>
          </el-col>

          <el-col :xs="24" :lg="8">
            <!-- 营养信息 -->
            <div v-if="nutrition" class="nutrition-section card">
              <h3 class="section-title">
                <el-icon><DataBoard /></el-icon>
                营养信息
              </h3>
              <div class="nutrition-list">
                <div v-if="nutrition.calories" class="nutrition-item">
                  <span class="nutrition-label">卡路里</span>
                  <span class="nutrition-value">{{ nutrition.calories }}kcal</span>
                </div>
                <div v-if="nutrition.protein" class="nutrition-item">
                  <span class="nutrition-label">蛋白质</span>
                  <span class="nutrition-value">{{ nutrition.protein }}g</span>
                </div>
                <div v-if="nutrition.carbohydrates" class="nutrition-item">
                  <span class="nutrition-label">碳水化合物</span>
                  <span class="nutrition-value">{{ nutrition.carbohydrates }}g</span>
                </div>
                <div v-if="nutrition.fat" class="nutrition-item">
                  <span class="nutrition-label">脂肪</span>
                  <span class="nutrition-value">{{ nutrition.fat }}g</span>
                </div>
              </div>
            </div>

            <!-- 相关食谱 -->
            <div class="related-recipes-section card">
              <h3 class="section-title">
                <el-icon><Dish /></el-icon>
                相关推荐
              </h3>
              <div class="related-recipes">
                <div
                  v-for="relatedRecipe in relatedRecipes"
                  :key="relatedRecipe.id"
                  class="related-item"
                  @click="router.push(`/recipe/${relatedRecipe.id}`)"
                >
                  <el-image
                    :src="getFileUrl(relatedRecipe.coverImage) || '/default-recipe.jpg'"
                    :alt="relatedRecipe.title"
                    fit="cover"
                    class="related-image"
                  />
                  <div class="related-info">
                    <h5>{{ relatedRecipe.title }}</h5>
                    <p>{{ getDifficultyText(relatedRecipe.difficulty) }}</p>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 评论区域 -->
        <div class="comments-section card">
          <CommentList :recipe-id="recipeId" />
        </div>

        <!-- 评分区域 -->
        <div class="rating-section card">
          <div class="section-header">
            <h3 class="section-title">
              <el-icon><Star /></el-icon>
              食谱评分
            </h3>
          </div>
          
          <el-row :gutter="30">
            <el-col :xs="24" :lg="12">
              <!-- 评分统计 -->
              <RatingStats 
                :recipe-id="recipeId"
                @add-rating="showRatingDialog = true"
                @edit-rating="showRatingDialog = true"
              />
            </el-col>
            
            <el-col :xs="24" :lg="12">
              <!-- 评分输入 -->
              <div v-if="!userRating" class="rating-input-section">
                <h4>为这道食谱评分</h4>
                <RatingInput 
                  :recipe-id="recipeId"
                  :show-title="false"
                  @submit="handleRatingSubmit"
                />
              </div>
              
              <!-- 用户已评分显示 -->
              <div v-else class="user-rated-section">
                <h4>你的评分</h4>
                <div class="user-rating-display">
                  <RatingDisplay 
                    :rating="userRating.score"
                    size="large"
                    :show-text="true"
                  />
                  <div class="rating-actions">
                    <el-button size="small" @click="showRatingDialog = true">
                      修改评分
                    </el-button>
                    <el-button size="small" type="danger" @click="handleDeleteRating">
                      删除评分
                    </el-button>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-container">
        <el-empty description="食谱不存在或已被删除">
          <el-button type="primary" @click="router.push('/recipes')">
            返回食谱列表
          </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Timer,
  User,
  View,
  Star,
  Collection,
  Share,
  Dish,
  Document,
  Warning,
  DataBoard
} from '@element-plus/icons-vue'
import CommentList from '@/components/Comment/CommentList.vue'
import RatingStats from '@/components/Rating/RatingStats.vue'
import RatingInput from '@/components/Rating/RatingInput.vue'
import RatingDisplay from '@/components/Rating/RatingDisplay.vue'
import type { Recipe, Ingredient, Instruction, Nutrition } from '@/types/recipe'
import type { Rating } from '@/types/rating'
import { getUserRating, deleteRating } from '@/api/rating'
import { recipesApi } from '@/api/recipes'
import { getFileUrl } from '@/utils/file'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const recipe = ref<Recipe | null>(null)
const ingredients = ref<Ingredient[]>([])
const instructions = ref<Instruction[]>([])
const nutrition = ref<Nutrition | null>(null)
const relatedRecipes = ref<Recipe[]>([])
const isLiked = ref(false)
const isFavorited = ref(false)
const likeLoading = ref(false)
const favoriteLoading = ref(false)
const userRating = ref<Rating | null>(null)
const showRatingDialog = ref(false)

// 计算属性
const recipeId = computed(() => Number(route.params.id))

// 获取食谱详情
const fetchRecipeDetail = async () => {
  loading.value = true
  try {
    // 调用API获取食谱详情
    const { data } = await recipesApi.getRecipeDetail(recipeId.value)
    
    if (data) {
      recipe.value = data
      ingredients.value = data.ingredients || []
      instructions.value = data.instructions || []
      nutrition.value = data.nutrition
      isLiked.value = data.isLiked || false
      isFavorited.value = data.isFavorited || false
      
      // 获取相关推荐食谱
      await fetchRelatedRecipes()
    } else {
      ElMessage.error('食谱不存在')
    }

  } catch (error) {
    console.error('获取食谱详情失败:', error)
    ElMessage.error('获取食谱详情失败')
  } finally {
    loading.value = false
  }
}

// 获取相关推荐食谱
const fetchRelatedRecipes = async () => {
  try {
    const { data } = await recipesApi.getRelatedRecipes(recipeId.value)
    relatedRecipes.value = data || []
  } catch (error) {
    console.error('获取相关推荐失败:', error)
  }
}

// 检查用户操作状态
const checkUserActions = async () => {
  // 这里应该调用API检查用户是否已点赞/收藏
  isLiked.value = false
  isFavorited.value = false
}

// 切换点赞状态
const toggleLike = async () => {
  likeLoading.value = true
  try {
    // 调用点赞API
    const { data: newLikeStatus } = await recipesApi.toggleLike(recipeId.value)
    isLiked.value = newLikeStatus
    
    if (recipe.value) {
      recipe.value.likes = (recipe.value.likes || 0) + (newLikeStatus ? 1 : -1)
    }
    ElMessage.success(newLikeStatus ? '点赞成功' : '取消点赞')
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    likeLoading.value = false
  }
}

// 切换收藏状态
const toggleFavorite = async () => {
  favoriteLoading.value = true
  try {
    // 调用收藏API
    if (isFavorited.value) {
      await recipesApi.unfavoriteRecipe(recipeId.value)
      isFavorited.value = false
      ElMessage.success('取消收藏')
    } else {
      await recipesApi.favoriteRecipe(recipeId.value)
      isFavorited.value = true
      ElMessage.success('收藏成功')
    }
    
    if (recipe.value) {
      recipe.value.favorites = (recipe.value.favorites || 0) + (isFavorited.value ? 1 : -1)
    }
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    favoriteLoading.value = false
  }
}

// 分享食谱
const shareRecipe = () => {
  if (navigator.share) {
    navigator.share({
      title: recipe.value?.title,
      text: recipe.value?.description,
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href)
    ElMessage.success('链接已复制到剪贴板')
  }
}

// 工具函数
const getTotalTime = (): number => {
  if (!recipe.value) return 0
  return (recipe.value.prepTime || 0) + (recipe.value.cookTime || 0)
}

const getDifficultyText = (difficulty: number): string => {
  const difficultyMap: Record<number, string> = {
    1: '简单',
    2: '中等',
    3: '困难'
  }
  return difficultyMap[difficulty] || '未知'
}

const getTagList = (tags?: string): string[] => {
  return tags ? tags.split(',').filter(tag => tag.trim()) : []
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 组件挂载
onMounted(() => {
  fetchRecipeDetail()
})

// 评分相关逻辑
const handleRatingSubmit = async (score: number) => {
  try {
    // 调用API提交评分，注意getUserRating函数签名
    // 这里应该调用createOrUpdateRating而不是getUserRating
    ElMessage.success('评分提交成功')
    showRatingDialog.value = false
  } catch (error) {
    ElMessage.error('评分提交失败')
  }
}

const handleDeleteRating = async () => {
  try {
    // 调用API删除评分
    await deleteRating(recipeId.value)
    ElMessage.success('评分删除成功')
    showRatingDialog.value = false
  } catch (error) {
    ElMessage.error('评分删除失败')
  }
}
</script>

<style scoped>
.recipe-detail-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  padding: 20px 0;
}

.loading-container,
.error-container {
  padding: 60px 20px;
  text-align: center;
}

/* 食谱头部 */
.recipe-header {
  margin-bottom: 30px;
  padding: 30px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.breadcrumb :deep(.el-breadcrumb__item) {
  cursor: pointer;
}

.header-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
  margin-bottom: 30px;
}

.recipe-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 15px;
  line-height: 1.3;
}

.recipe-description {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 20px;
}

.recipe-meta {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-weight: 500;
}

.difficulty {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
}

.difficulty-1 {
  background: #f0f9ff;
  color: #0ea5e9;
}

.difficulty-2 {
  background: #fefce8;
  color: #eab308;
}

.difficulty-3 {
  background: #fef2f2;
  color: #ef4444;
}

.recipe-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.recipe-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.recipe-tag {
  background: var(--primary-color);
  border: none;
  color: white;
}

.recipe-image {
  position: relative;
}

.cover-image {
  width: 100%;
  height: 300px;
  border-radius: var(--border-radius-md);
  object-fit: cover;
}

/* 作者信息 */
.author-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.author-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.author-details h4 {
  margin: 0 0 5px;
  color: var(--text-primary);
  font-weight: 600;
}

.author-details p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

/* 内容区域 */
.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
}

/* 食材清单 */
.ingredients-section {
  margin-bottom: 30px;
  padding: 25px;
}

.ingredients-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: var(--bg-light);
  border-radius: var(--border-radius-sm);
  border-left: 3px solid var(--primary-color);
}

.ingredient-name {
  font-weight: 500;
  color: var(--text-primary);
}

.ingredient-amount {
  font-weight: 600;
  color: var(--primary-color);
}

.ingredient-unit,
.ingredient-notes {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* 制作步骤 */
.instructions-section {
  margin-bottom: 30px;
  padding: 25px;
}

.instructions-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.instruction-item {
  display: flex;
  gap: 20px;
  padding: 20px;
  background: var(--bg-light);
  border-radius: var(--border-radius-sm);
}

.step-number {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-description {
  color: var(--text-primary);
  line-height: 1.6;
  margin-bottom: 10px;
}

.step-duration {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* 小贴士 */
.tips-section {
  margin-bottom: 30px;
  padding: 25px;
}

.tips-content {
  color: var(--text-primary);
  line-height: 1.6;
  background: #fff7ed;
  padding: 15px;
  border-radius: var(--border-radius-sm);
  border-left: 3px solid #f97316;
}

/* 营养信息 */
.nutrition-section {
  margin-bottom: 30px;
  padding: 25px;
}

.nutrition-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.nutrition-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: var(--bg-light);
  border-radius: var(--border-radius-sm);
}

.nutrition-label {
  color: var(--text-secondary);
}

.nutrition-value {
  font-weight: 600;
  color: var(--primary-color);
}

/* 相关食谱 */
.related-recipes-section {
  padding: 25px;
}

.related-recipes {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.related-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background: var(--bg-light);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.related-item:hover {
  background: var(--bg-hover);
  transform: translateY(-2px);
}

.related-image {
  width: 80px;
  height: 60px;
  border-radius: var(--border-radius-sm);
  object-fit: cover;
}

.related-info {
  flex: 1;
}

.related-info h5 {
  margin: 0 0 5px;
  color: var(--text-primary);
  font-weight: 500;
}

.related-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* 评分区域 */
.rating-section {
  margin-bottom: 30px;
  padding: 25px;
}

.section-header {
  margin-bottom: 20px;
}

.rating-input-section,
.user-rated-section {
  background: var(--bg-light);
  border-radius: var(--border-radius-sm);
  padding: 20px;
}

.rating-input-section h4,
.user-rated-section h4 {
  margin: 0 0 15px;
  color: var(--text-primary);
  font-weight: 600;
}

.user-rating-display {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.rating-actions {
  display: flex;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .author-section {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: space-around;
  }
  
  .recipe-meta {
    flex-direction: column;
    gap: 10px;
  }
  
  .ingredients-list {
    grid-template-columns: 1fr;
  }
  
  .ingredient-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style> 