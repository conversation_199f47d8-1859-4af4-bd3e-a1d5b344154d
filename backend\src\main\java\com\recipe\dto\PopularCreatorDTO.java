package com.recipe.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 热门创作者DTO
 */
@Data
@Schema(description = "热门创作者信息")
public class PopularCreatorDTO {

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "个人简介")
    private String bio;

    @Schema(description = "食谱数量")
    private Long recipeCount;

    @Schema(description = "总浏览量")
    private Long totalViews;

    @Schema(description = "总点赞数")
    private Long totalLikes;

    @Schema(description = "总收藏数")
    private Long totalFavorites;

    @Schema(description = "粉丝数")
    private Long followersCount;

    @Schema(description = "关注数")
    private Long followingCount;

    @Schema(description = "最新食谱标题")
    private String latestRecipeTitle;

    @Schema(description = "最新食谱封面")
    private String latestRecipeCover;

    @Schema(description = "最新发布时间")
    private LocalDateTime latestPublishTime;

    @Schema(description = "是否已关注（仅登录用户）")
    private Boolean isFollowed;
}
