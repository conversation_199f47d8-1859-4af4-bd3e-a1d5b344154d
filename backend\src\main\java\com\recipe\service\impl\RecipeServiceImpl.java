package com.recipe.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.recipe.dto.MyRecipeQueryDTO;
import com.recipe.dto.PaginatedResponseDTO;
import com.recipe.dto.RecipeCreateRequestDTO;
import com.recipe.dto.RecipeDTO;
import com.recipe.dto.RecipeQueryDTO;
import com.recipe.dto.UserRecipeStatsDTO;
import com.recipe.entity.Recipe;
import com.recipe.mapper.RecipeMapper;
import com.recipe.service.IRecipeService;
import com.recipe.utils.RecipeConverter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 食谱服务实现类
 */
@Service
public class RecipeServiceImpl extends ServiceImpl<RecipeMapper, Recipe> implements IRecipeService {

    @Autowired
    private RecipeMapper recipeMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 分页查询食谱
     * 支持关键词搜索、分类筛选、难度筛选和排序
     * 
     * @param queryDTO 查询参数
     * @return 分页的食谱列表
     */
    @Override
    public PaginatedResponseDTO<RecipeDTO> getRecipes(RecipeQueryDTO queryDTO) {
        // 查询食谱列表
        List<Recipe> recipes = recipeMapper.selectRecipesWithPagination(queryDTO);

        // 查询总数
        Long totalElements = recipeMapper.countRecipes(queryDTO);

        // 转换为DTO
        List<RecipeDTO> recipeDTOs = RecipeConverter.toDTOList(recipes);

        // 构建分页响应
        PaginatedResponseDTO<RecipeDTO> response = new PaginatedResponseDTO<>();
        response.setContent(recipeDTOs);
        response.setCurrentPage(queryDTO.getPage());
        response.setPageSize(queryDTO.getSize());
        response.setTotalElements(totalElements);
        response.setLast(queryDTO.getPage() * queryDTO.getSize() >= totalElements);

        return response;
    }

    /**
     * 获取热门食谱
     * 使用复杂的热度计算公式，需要调用mapper层的手写SQL
     * 
     * @param limit 限制数量
     * @return 热门食谱列表
     */
    @Override
    public List<Recipe> getPopularRecipes(Integer limit) {
        // 热度计算涉及复杂的权重计算，使用mapper层的SQL
        return recipeMapper.selectPopularRecipes(limit);
    }

    /**
     * 增加食谱浏览量
     * 使用MyBatis-Plus的UpdateWrapper进行原子性更新
     * 
     * @param id 食谱ID
     */
    @Override
    @Transactional
    public void incrementViews(Long id) {
        // 使用MP的UpdateWrapper进行原子性浏览量增加
        UpdateWrapper<Recipe> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id)
                .eq("deleted", 0)
                .setSql("view_count = view_count + 1");
        this.update(updateWrapper);
    }

    /**
     * 获取推荐食谱
     * 使用复杂的推荐算法，需要调用mapper层的手写SQL
     * 
     * @param limit 限制数量
     * @return 推荐食谱列表
     */
    @Override
    public List<Recipe> getRecommendedRecipes(Integer limit) {
        // 推荐算法涉及时间因子和权重计算，使用mapper层的SQL
        return recipeMapper.selectRecommendedRecipes(limit);
    }

    /**
     * 获取最新食谱
     * 使用MyBatis-Plus的QueryWrapper进行简单查询
     * 
     * @param limit 限制数量
     * @return 最新食谱列表
     */
    @Override
    public List<Recipe> getLatestRecipes(Integer limit) {
        // 简单的按时间排序查询，使用MP的QueryWrapper
        QueryWrapper<Recipe> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                .eq("is_private", 0)
                .eq("deleted", 0)
                .orderByDesc("created_at")
                .last("LIMIT " + limit);

        List<Recipe> recipes = this.list(queryWrapper);

        // 直接返回查询结果
        return recipes;
    }

    /**
     * 根据ID获取食谱详情（包含创建者和分类信息）
     * 需要关联查询用户和分类信息，调用mapper层
     * 
     * @param id 食谱ID
     * @return 食谱详情
     */
    @Override
    public Recipe getRecipeWithDetails(Long id) {
        // 涉及多表关联查询，使用mapper层的SQL
        return recipeMapper.selectRecipeWithDetails(id);
    }

    /**
     * 获取食谱统计信息
     * 使用MyBatis-Plus的QueryWrapper进行简单查询
     * 
     * @param id 食谱ID
     * @return 统计信息
     */
    @Override
    public Recipe getRecipeStats(Long id) {
        // 简单的字段查询，使用MP的QueryWrapper
        QueryWrapper<Recipe> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "view_count", "like_count", "favorite_count",
                "comment_count", "average_rating", "rating_count")
                .eq("id", id)
                .eq("deleted", 0);

        return this.getOne(queryWrapper);
    }

    /**
     * 根据分类ID获取食谱列表
     * 使用MyBatis-Plus的QueryWrapper进行简单查询
     * 
     * @param categoryId 分类ID
     * @param limit      限制数量
     * @return 食谱列表
     */
    @Override
    public List<Recipe> getRecipesByCategory(Long categoryId, Integer limit) {
        QueryWrapper<Recipe> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category_id", categoryId)
                .eq("status", 1)
                .eq("is_private", 0)
                .eq("deleted", 0)
                .orderByDesc("created_at")
                .last("LIMIT " + limit);

        return this.list(queryWrapper);
    }

    /**
     * 根据用户ID获取用户的食谱列表
     * 使用MyBatis-Plus的QueryWrapper进行简单查询
     * 
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 食谱列表
     */
    @Override
    public List<Recipe> getRecipesByUser(Long userId, Integer limit) {
        QueryWrapper<Recipe> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("status", 1)
                .eq("deleted", 0)
                .orderByDesc("created_at")
                .last("LIMIT " + limit);

        return this.list(queryWrapper);
    }

    /**
     * 搜索食谱
     * 使用MyBatis-Plus的QueryWrapper进行关键词搜索
     * 
     * @param keyword 搜索关键词
     * @param limit   限制数量
     * @return 食谱列表
     */
    @Override
    public List<Recipe> searchRecipes(String keyword, Integer limit) {
        QueryWrapper<Recipe> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper.like("title", keyword)
                .or()
                .like("description", keyword)
                .or()
                .like("tags", keyword))
                .eq("status", 1)
                .eq("is_private", 0)
                .eq("deleted", 0)
                .orderByDesc("view_count")
                .last("LIMIT " + limit);

        return this.list(queryWrapper);
    }

    /**
     * 获取用户收藏的食谱数量
     * 使用MyBatis-Plus的QueryWrapper进行统计查询
     * 
     * @param userId 用户ID
     * @return 收藏数量
     */
    @Override
    public Long getUserFavoriteCount(Long userId) {
        // 这里需要关联favorites表，实际应该在FavoriteService中实现
        // 此处仅作示例，展示如何使用MP进行count查询
        QueryWrapper<Recipe> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("deleted", 0);

        return this.count(queryWrapper);
    }

    /**
     * 增加食谱收藏数
     * 
     * @param recipeId 食谱ID
     */
    @Override
    @Transactional
    public void incrementFavoriteCount(Long recipeId) {
        UpdateWrapper<Recipe> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", recipeId)
                .eq("deleted", 0)
                .setSql("favorite_count = favorite_count + 1");
        this.update(updateWrapper);
    }

    /**
     * 减少食谱收藏数
     * 
     * @param recipeId 食谱ID
     */
    @Override
    @Transactional
    public void decrementFavoriteCount(Long recipeId) {
        UpdateWrapper<Recipe> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", recipeId)
                .eq("deleted", 0)
                .setSql("favorite_count = GREATEST(favorite_count - 1, 0)");
        this.update(updateWrapper);
    }

    /**
     * 获取用户的食谱列表（支持搜索、筛选、分页、排序）
     * 
     * @param userId   用户ID
     * @param queryDTO 查询参数
     * @return 分页的食谱列表
     */
    @Override
    public PaginatedResponseDTO<RecipeDTO> getMyRecipes(Long userId, MyRecipeQueryDTO queryDTO) {
        // 查询总数
        Long total = recipeMapper.countMyRecipes(userId, queryDTO);

        if (total == 0) {
            return new PaginatedResponseDTO<>(List.of(), queryDTO.getPage(), queryDTO.getSize(), 0L);
        }

        // 查询数据
        List<Recipe> recipes = recipeMapper.selectMyRecipes(userId, queryDTO);

        // 转换为DTO
        List<RecipeDTO> recipeDTOs = RecipeConverter.toDTOList(recipes);

        return new PaginatedResponseDTO<>(recipeDTOs, queryDTO.getPage(), queryDTO.getSize(), total);
    }

    /**
     * 获取用户食谱统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public UserRecipeStatsDTO getUserRecipeStats(Long userId) {
        return recipeMapper.selectUserRecipeStats(userId);
    }

    /**
     * 发布食谱
     * 
     * @param recipeId 食谱ID
     * @param userId   用户ID
     */
    @Override
    @Transactional
    public void publishRecipe(Long recipeId, Long userId) {
        // 验证食谱所有权
        Recipe recipe = this.getById(recipeId);
        if (recipe == null || !recipe.getUserId().equals(userId)) {
            throw new RuntimeException("食谱不存在或无权限操作");
        }

        // 更新状态为已发布
        recipeMapper.updateRecipeStatus(recipeId, 1);
    }

    /**
     * 下架食谱
     * 
     * @param recipeId 食谱ID
     * @param userId   用户ID
     */
    @Override
    @Transactional
    public void unpublishRecipe(Long recipeId, Long userId) {
        // 验证食谱所有权
        Recipe recipe = this.getById(recipeId);
        if (recipe == null || !recipe.getUserId().equals(userId)) {
            throw new RuntimeException("食谱不存在或无权限操作");
        }

        // 更新状态为已下架
        recipeMapper.updateRecipeStatus(recipeId, 2);
    }

    /**
     * 复制食谱
     * 
     * @param recipeId 食谱ID
     * @param userId   用户ID
     * @return 新创建的食谱
     */
    @Override
    @Transactional
    public Recipe duplicateRecipe(Long recipeId, Long userId) {
        // 获取原食谱
        Recipe originalRecipe = this.getById(recipeId);
        if (originalRecipe == null) {
            throw new RuntimeException("食谱不存在");
        }

        // 创建新食谱
        Recipe newRecipe = new Recipe();
        BeanUtils.copyProperties(originalRecipe, newRecipe);

        // 重置关键字段
        newRecipe.setId(null);
        newRecipe.setUserId(userId);
        newRecipe.setTitle(originalRecipe.getTitle() + " (副本)");
        newRecipe.setStatus(0); // 设为草稿
        newRecipe.setViewCount(0);
        newRecipe.setLikeCount(0);
        newRecipe.setFavoriteCount(0);
        newRecipe.setCommentCount(0);
        newRecipe.setCreatedAt(LocalDateTime.now());
        newRecipe.setUpdatedAt(LocalDateTime.now());

        // 保存新食谱
        this.save(newRecipe);

        return newRecipe;
    }

    /**
     * 删除食谱
     * 
     * @param recipeId 食谱ID
     * @param userId   用户ID
     */
    @Override
    @Transactional
    public void deleteRecipe(Long recipeId, Long userId) {
        // 验证食谱所有权
        Recipe recipe = this.getById(recipeId);
        if (recipe == null || !recipe.getUserId().equals(userId)) {
            throw new RuntimeException("食谱不存在或无权限操作");
        }

        // 逻辑删除
        this.removeById(recipeId);
    }

    /**
     * 创建食谱
     * 
     * @param recipe 食谱信息
     * @return 创建的食谱
     */
    @Override
    @Transactional
    public Recipe createRecipe(Recipe recipe) {
        recipe.setCreatedAt(LocalDateTime.now());
        recipe.setUpdatedAt(LocalDateTime.now());
        recipe.setViewCount(0);
        recipe.setLikeCount(0);
        recipe.setFavoriteCount(0);
        recipe.setCommentCount(0);
        recipe.setDeleted(0);

        this.save(recipe);
        return recipe;
    }

    /**
     * 更新食谱
     * 
     * @param recipe 食谱信息
     * @return 更新的食谱
     */
    @Override
    @Transactional
    public Recipe updateRecipe(Recipe recipe) {
        recipe.setUpdatedAt(LocalDateTime.now());
        this.updateById(recipe);
        return recipe;
    }

    /**
     * 获取热门食谱排行榜
     * 
     * @param type  排行榜类型：week-本周热门，month-本月热门，likes-最多点赞，favorites-最多收藏，views-最多浏览
     * @param limit 限制数量
     * @return 热门食谱排行榜
     */
    @Override
    public List<Recipe> getPopularRanking(String type, Integer limit) {
        return recipeMapper.selectPopularRanking(type, limit);
    }

    /**
     * 获取热门食谱统计数据
     * 
     * @return 统计数据
     */
    @Override
    public Object getPopularStats() {
        return recipeMapper.selectPopularStats();
    }

    /**
     * 根据创建请求创建食谱
     * 
     * @param createRequest 创建请求
     * @param userId        用户ID
     * @return 创建的食谱
     */
    @Override
    @Transactional
    public Recipe createRecipeFromRequest(RecipeCreateRequestDTO createRequest, Long userId) {
        Recipe recipe = new Recipe();

        // 设置基本信息
        recipe.setUserId(userId);
        recipe.setTitle(createRequest.getTitle());
        recipe.setDescription(createRequest.getDescription());
        // 只有当封面图片不为空时才设置
        if (createRequest.getCoverImage() != null && !createRequest.getCoverImage().trim().isEmpty()) {
            recipe.setCoverImage(createRequest.getCoverImage());
        }
        recipe.setPrepTime(createRequest.getPrepTime());
        recipe.setCookingTime(createRequest.getCookTime());
        recipe.setDifficulty(createRequest.getDifficulty());
        recipe.setServings(createRequest.getServings());
        recipe.setCategoryId(createRequest.getCategoryId());
        recipe.setTips(createRequest.getTips());
        recipe.setIsPrivate(createRequest.getIsPublic() == 0 ? 1 : 0); // 转换：前端1为公开，后端0为公开
        recipe.setStatus(1); // 发布状态

        // 转换食材列表为JSON
        try {
            String ingredientsJson = objectMapper.writeValueAsString(createRequest.getIngredients());
            recipe.setIngredients(ingredientsJson);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("食材信息序列化失败", e);
        }

        // 转换制作步骤为JSON
        try {
            String instructionsJson = objectMapper.writeValueAsString(createRequest.getInstructions());
            recipe.setInstructions(instructionsJson);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("制作步骤序列化失败", e);
        }

        // 转换标签为JSON字符串
        if (createRequest.getTags() != null && !createRequest.getTags().isEmpty()) {
            try {
                String tagsJson = objectMapper.writeValueAsString(createRequest.getTags());
                recipe.setTags(tagsJson);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("标签信息序列化失败", e);
            }
        }

        // 设置默认值
        recipe.setViewCount(0);
        recipe.setLikeCount(0);
        recipe.setFavoriteCount(0);
        recipe.setCommentCount(0);
        recipe.setDeleted(0);
        recipe.setCreatedAt(LocalDateTime.now());
        recipe.setUpdatedAt(LocalDateTime.now());

        // 保存食谱
        this.save(recipe);

        return recipe;
    }

    /**
     * 保存食谱草稿
     * 
     * @param createRequest 创建请求
     * @param userId        用户ID
     * @return 保存的草稿
     */
    @Override
    @Transactional
    public Recipe saveDraft(RecipeCreateRequestDTO createRequest, Long userId) {
        Recipe recipe = new Recipe();

        // 设置基本信息
        recipe.setUserId(userId);
        recipe.setTitle(createRequest.getTitle() != null ? createRequest.getTitle() : "未命名草稿");
        recipe.setDescription(createRequest.getDescription());
        // 只有当封面图片不为空时才设置
        if (createRequest.getCoverImage() != null && !createRequest.getCoverImage().trim().isEmpty()) {
            recipe.setCoverImage(createRequest.getCoverImage());
        }
        recipe.setPrepTime(createRequest.getPrepTime());
        recipe.setCookingTime(createRequest.getCookTime());
        recipe.setDifficulty(createRequest.getDifficulty());
        recipe.setServings(createRequest.getServings());
        recipe.setCategoryId(createRequest.getCategoryId());
        recipe.setTips(createRequest.getTips());
        recipe.setIsPrivate(createRequest.getIsPublic() == 0 ? 1 : 0); // 转换：前端1为公开，后端0为公开
        recipe.setStatus(0); // 草稿状态

        // 转换食材列表为JSON（允许空值）
        if (createRequest.getIngredients() != null && !createRequest.getIngredients().isEmpty()) {
            try {
                String ingredientsJson = objectMapper.writeValueAsString(createRequest.getIngredients());
                recipe.setIngredients(ingredientsJson);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("食材信息序列化失败", e);
            }
        }

        // 转换制作步骤为JSON（允许空值）
        if (createRequest.getInstructions() != null && !createRequest.getInstructions().isEmpty()) {
            try {
                String instructionsJson = objectMapper.writeValueAsString(createRequest.getInstructions());
                recipe.setInstructions(instructionsJson);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("制作步骤序列化失败", e);
            }
        }

        // 转换标签为JSON字符串（允许空值）
        if (createRequest.getTags() != null && !createRequest.getTags().isEmpty()) {
            try {
                String tagsJson = objectMapper.writeValueAsString(createRequest.getTags());
                recipe.setTags(tagsJson);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("标签信息序列化失败", e);
            }
        }

        // 设置默认值
        recipe.setViewCount(0);
        recipe.setLikeCount(0);
        recipe.setFavoriteCount(0);
        recipe.setCommentCount(0);
        recipe.setDeleted(0);
        recipe.setCreatedAt(LocalDateTime.now());
        recipe.setUpdatedAt(LocalDateTime.now());

        // 保存草稿
        this.save(recipe);

        return recipe;
    }

    /**
     * 获取相关推荐食谱
     * 
     * @param recipeId 当前食谱ID
     * @param limit    限制数量
     * @return 相关食谱列表
     */
    @Override
    public List<Recipe> getRelatedRecipes(Long recipeId, Integer limit) {
        try {
            // 先获取当前食谱的分类ID
            Recipe currentRecipe = this.getById(recipeId);
            if (currentRecipe == null || currentRecipe.getCategoryId() == null) {
                // 如果食谱不存在或没有分类，返回热门食谱
                return getPopularRecipes(limit);
            }

            // 根据分类获取相关食谱
            List<Recipe> relatedRecipes = recipeMapper.selectRelatedRecipes(
                    currentRecipe.getCategoryId(), recipeId, limit);

            // 如果相关食谱数量不够，用热门食谱补充
            if (relatedRecipes.size() < limit) {
                List<Recipe> popularRecipes = getPopularRecipes(limit - relatedRecipes.size());
                // 过滤掉已存在的食谱
                for (Recipe popular : popularRecipes) {
                    if (relatedRecipes.stream().noneMatch(r -> r.getId().equals(popular.getId()))
                            && !popular.getId().equals(recipeId)) {
                        relatedRecipes.add(popular);
                        if (relatedRecipes.size() >= limit) {
                            break;
                        }
                    }
                }
            }

            return relatedRecipes;
        } catch (Exception e) {
            // 如果出错，返回热门食谱作为fallback
            return getPopularRecipes(limit);
        }
    }
}