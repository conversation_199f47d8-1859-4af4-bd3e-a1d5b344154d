package com.recipe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.recipe.dto.MyRecipeQueryDTO;
import com.recipe.dto.PaginatedResponseDTO;
import com.recipe.dto.RecipeDTO;
import com.recipe.dto.RecipeQueryDTO;
import com.recipe.dto.UserRecipeStatsDTO;
import com.recipe.dto.LatestRecipeDTO;
import com.recipe.entity.Recipe;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 食谱数据访问层
 */
@Mapper
public interface RecipeMapper extends BaseMapper<Recipe> {

    /**
     * 分页查询食谱
     * 支持关键词搜索、分类筛选、难度筛选和排序
     * 
     * @param queryDTO 查询参数
     * @return 食谱列表
     */
    List<Recipe> selectRecipesWithPagination(@Param("query") RecipeQueryDTO queryDTO);

    /**
     * 统计食谱总数
     * 
     * @param queryDTO 查询参数
     * @return 总数量
     */
    Long countRecipes(@Param("query") RecipeQueryDTO queryDTO);

    /**
     * 获取热门食谱
     * 根据浏览量、点赞数、收藏数等综合计算热度
     * 
     * @param limit 限制数量
     * @return 热门食谱列表
     */
    @Select("""
                SELECT r.*, u.username as creator_name, u.avatar as creator_avatar, rc.name as category_name
                FROM recipes r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN recipe_categories rc ON r.category_id = rc.id
                WHERE r.status = 1 AND r.is_private = 0 AND r.deleted = 0
                ORDER BY (r.view_count * 1 + r.like_count * 3 + r.favorite_count * 5) DESC
                LIMIT #{limit}
            """)
    List<Recipe> selectPopularRecipes(@Param("limit") Integer limit);

    /**
     * 增加食谱浏览量
     * 
     * @param id 食谱ID
     */
    @Update("UPDATE recipes SET view_count = view_count + 1 WHERE id = #{id}")
    void incrementViews(@Param("id") Long id);

    /**
     * 获取推荐食谱
     * 基于创建时间和热度的综合推荐
     * 
     * @param limit 限制数量
     * @return 推荐食谱列表
     */
    @Select("""
                SELECT r.*, u.username as creator_name, u.avatar as creator_avatar, rc.name as category_name
                FROM recipes r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN recipe_categories rc ON r.category_id = rc.id
                WHERE r.status = 1 AND r.is_private = 0 AND r.deleted = 0
                ORDER BY (r.view_count * 0.3 + r.like_count * 0.5 + r.favorite_count * 0.2 +
                         DATEDIFF(NOW(), r.created_at) * -0.1) DESC
                LIMIT #{limit}
            """)
    List<Recipe> selectRecommendedRecipes(@Param("limit") Integer limit);

    /**
     * 根据ID获取食谱详情（包含创建者和分类信息）
     * 
     * @param id 食谱ID
     * @return 食谱详情
     */
    @Select("""
                SELECT r.*, u.username as creator_name, u.avatar as creator_avatar,
                       u.nickname as creator_nickname, rc.name as category_name
                FROM recipes r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN recipe_categories rc ON r.category_id = rc.id
                WHERE r.id = #{id} AND r.deleted = 0
            """)
    Recipe selectRecipeWithDetails(@Param("id") Long id);

    /**
     * 获取食谱统计信息
     * 
     * @param id 食谱ID
     * @return 统计信息
     */
    @Select("""
                SELECT id, view_count, like_count, favorite_count, comment_count,
                       average_rating, rating_count
                FROM recipes
                WHERE id = #{id} AND deleted = 0
            """)
    Recipe selectRecipeStats(@Param("id") Long id);

    /**
     * 根据条件查询用户食谱列表
     * 
     * @param userId   用户ID
     * @param queryDTO 查询条件
     * @return 食谱列表
     */
    List<Recipe> selectMyRecipes(@Param("userId") Long userId, @Param("query") MyRecipeQueryDTO queryDTO);

    /**
     * 根据条件统计用户食谱数量
     * 
     * @param userId   用户ID
     * @param queryDTO 查询条件
     * @return 总数量
     */
    Long countMyRecipes(@Param("userId") Long userId, @Param("query") MyRecipeQueryDTO queryDTO);

    /**
     * 获取用户食谱统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Select("""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as published,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as drafts,
                    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as unpublished,
                    COALESCE(SUM(view_count), 0) as totalViews,
                    COALESCE(SUM(like_count), 0) as totalLikes,
                    COALESCE(SUM(favorite_count), 0) as totalFavorites,
                    COALESCE(SUM(comment_count), 0) as totalComments
                FROM recipes
                WHERE user_id = #{userId} AND deleted = 0
            """)
    UserRecipeStatsDTO selectUserRecipeStats(@Param("userId") Long userId);

    /**
     * 更新食谱状态
     * 
     * @param id     食谱ID
     * @param status 状态
     */
    @Update("UPDATE recipes SET status = #{status}, updated_at = NOW() WHERE id = #{id}")
    void updateRecipeStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 获取热门食谱排行榜
     * 
     * @param type  排行榜类型：week-本周热门，month-本月热门，likes-最多点赞，favorites-最多收藏，views-最多浏览
     * @param limit 限制数量
     * @return 热门食谱排行榜
     */
    List<Recipe> selectPopularRanking(@Param("type") String type, @Param("limit") Integer limit);

    /**
     * 获取热门食谱统计数据
     * 
     * @return 统计数据
     */
    Object selectPopularStats();

    /**
     * 增加食谱点赞数
     * 
     * @param id 食谱ID
     */
    @Update("UPDATE recipes SET like_count = like_count + 1 WHERE id = #{id}")
    void incrementLikeCount(@Param("id") Long id);

    /**
     * 减少食谱点赞数
     * 
     * @param id 食谱ID
     */
    @Update("UPDATE recipes SET like_count = GREATEST(like_count - 1, 0) WHERE id = #{id}")
    void decrementLikeCount(@Param("id") Long id);

    /**
     * 获取相关推荐食谱
     * 
     * @param categoryId      分类ID
     * @param excludeRecipeId 排除的食谱ID
     * @param limit           限制数量
     * @return 相关食谱列表
     */
    @Select("""
                SELECT r.*, u.username as creator_name, u.avatar as creator_avatar, rc.name as category_name
                FROM recipes r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN recipe_categories rc ON r.category_id = rc.id
                WHERE r.category_id = #{categoryId}
                AND r.id != #{excludeRecipeId}
                AND r.status = 1 AND r.is_private = 0 AND r.deleted = 0
                ORDER BY (r.view_count * 1 + r.like_count * 2 + r.favorite_count * 3) DESC
                LIMIT #{limit}
            """)
    List<Recipe> selectRelatedRecipes(@Param("categoryId") Long categoryId,
            @Param("excludeRecipeId") Long excludeRecipeId,
            @Param("limit") Integer limit);

    /**
     * 获取最新食谱（包含创建者信息）
     * 
     * @param limit 限制数量
     * @return 最新食谱列表
     */
    @Select("""
                SELECT r.id, r.title, r.description, r.cover_image, r.prep_time, r.cook_time,
                       r.difficulty, r.servings, r.view_count as views, r.like_count as likes,
                       r.favorite_count as favorites, r.category_id, r.user_id as creator_id,
                       r.tags, r.created_at, r.updated_at,
                       u.username as creator_username, u.nickname as creator_nickname, u.avatar as creator_avatar,
                       rc.name as category_name
                FROM recipes r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN recipe_categories rc ON r.category_id = rc.id
                WHERE r.status = 1 AND r.is_private = 0 AND r.deleted = 0
                ORDER BY r.created_at DESC
                LIMIT #{limit}
            """)
    List<LatestRecipeDTO> selectLatestRecipes(@Param("limit") Integer limit);

    /**
     * 获取推荐食谱（基于用户喜好）
     * 
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 推荐食谱列表
     */
    @Select("""
                SELECT DISTINCT r.id, r.title, r.description, r.cover_image, r.prep_time, r.cook_time,
                       r.difficulty, r.servings, r.view_count as views, r.like_count as likes,
                       r.favorite_count as favorites, r.category_id, r.user_id as creator_id,
                       r.tags, r.created_at, r.updated_at,
                       u.username as creator_username, u.nickname as creator_nickname, u.avatar as creator_avatar,
                       rc.name as category_name
                FROM recipes r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN recipe_categories rc ON r.category_id = rc.id
                LEFT JOIN recipe_favorites rf ON r.id = rf.recipe_id
                LEFT JOIN follows f ON r.user_id = f.following_id
                WHERE r.status = 1 AND r.is_private = 0 AND r.deleted = 0
                AND (rf.user_id = #{userId} OR f.follower_id = #{userId} OR r.category_id IN (
                    SELECT DISTINCT r2.category_id FROM recipes r2
                    LEFT JOIN recipe_favorites rf2 ON r2.id = rf2.recipe_id
                    WHERE rf2.user_id = #{userId}
                ))
                ORDER BY (r.view_count * 0.3 + r.like_count * 0.5 + r.favorite_count * 0.2) DESC
                LIMIT #{limit}
            """)
    List<LatestRecipeDTO> selectRecommendedRecipes(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 获取热门食谱（用于首页）
     * 
     * @param limit 限制数量
     * @return 热门食谱列表
     */
    @Select("""
                SELECT r.id, r.title, r.description, r.cover_image, r.prep_time, r.cook_time,
                       r.difficulty, r.servings, r.view_count as views, r.like_count as likes,
                       r.favorite_count as favorites, r.category_id, r.user_id as creator_id,
                       r.tags, r.created_at, r.updated_at,
                       u.username as creator_username, u.nickname as creator_nickname, u.avatar as creator_avatar,
                       rc.name as category_name
                FROM recipes r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN recipe_categories rc ON r.category_id = rc.id
                WHERE r.status = 1 AND r.is_private = 0 AND r.deleted = 0
                ORDER BY (r.view_count * 1 + r.like_count * 3 + r.favorite_count * 5) DESC
                LIMIT #{limit}
            """)
    List<LatestRecipeDTO> selectPopularRecipesForHome(@Param("limit") Integer limit);

    /**
     * 获取总浏览量
     * 
     * @return 总浏览量
     */
    @Select("SELECT COALESCE(SUM(view_count), 0) FROM recipes WHERE status = 1 AND deleted = 0")
    Long getTotalViews();

    /**
     * 获取用户食谱总浏览量
     * 
     * @param userId 用户ID
     * @return 总浏览量
     */
    @Select("SELECT COALESCE(SUM(view_count), 0) FROM recipes WHERE user_id = #{userId} AND deleted = 0")
    Long getTotalViewsByUserId(@Param("userId") Long userId);

    /**
     * 获取用户食谱总点赞数
     * 
     * @param userId 用户ID
     * @return 总点赞数
     */
    @Select("SELECT COALESCE(SUM(like_count), 0) FROM recipes WHERE user_id = #{userId} AND deleted = 0")
    Long getTotalLikesByUserId(@Param("userId") Long userId);
}