import{d as Se,r as d,z as Re,D as ze,B as Ve,an as Ie,o as De,ag as Pe,ah as Ae,p as b,c as Z,e as o,f as e,w as t,j as u,n as s,h as T,G as Me,I as Ne,S as Oe,T as Le,t as r,_ as E,Q as Be,b as Fe,k as w,P as I,am as je,Z as Ue,ai as Ee,s as $e}from"./index-BDkHONMN.js";import{i as S,L as Qe}from"./index-BNz9ATsT.js";import{_ as Ge}from"./_plugin-vue_export-helper-DlAUqK2U.js";const qe={class:"content-stats-page"},Ke={class:"page-header"},We={class:"header-actions"},Xe={class:"metric-content"},Ze={class:"metric-info"},He={class:"metric-value"},Je={class:"metric-label"},Ye={class:"metric-trend"},et={class:"chart-header"},tt={class:"table-header"},at={class:"table-header"},lt={class:"ranking-header"},ot={class:"ranking-tabs"},nt={class:"content-info"},st={class:"content-cover"},rt=["src"],it={class:"content-details"},dt={class:"content-title"},ut={class:"content-meta"},ct={class:"content-category"},pt={class:"content-date"},ft={class:"author-info"},mt={class:"engagement-rate"},_t=Se({__name:"ContentStats",setup(vt){const H=Fe(),D=d(!1),P=d(!1),$=d("all"),Q=d("30days"),R=d(!1),A=d("views"),M=d(),N=d(),O=d(),L=d(),B=d();let _=null,v=null,g=null,h=null,C=null;const J=d([{key:"totalContent",label:"总内容数",value:3426,trend:12.3,icon:Re,bgColor:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",iconColor:"#fff"},{key:"totalViews",label:"总浏览量",value:856420,trend:15.7,icon:ze,bgColor:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",iconColor:"#fff"},{key:"totalLikes",label:"总点赞数",value:45632,trend:-2.1,icon:Ve,bgColor:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",iconColor:"#fff"},{key:"avgEngagement",label:"平均互动率",value:8.5,trend:5.8,icon:Ie,bgColor:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",iconColor:"#fff"}]),Y=d([{metric:"浏览量",avg:1250,median:850,top10:15420},{metric:"点赞数",avg:85,median:45,top10:856},{metric:"评论数",avg:12,median:8,top10:125},{metric:"分享数",avg:6,median:3,top10:68},{metric:"收藏数",avg:25,median:15,top10:245},{metric:"互动率",avg:8.5,median:6.2,top10:25.8}]),ee=d([{period:"本周",activeCreators:580,newCreators:45,avgPublish:2.3},{period:"上周",activeCreators:620,newCreators:52,avgPublish:2.1},{period:"上上周",activeCreators:545,newCreators:38,avgPublish:2.5},{period:"3周前",activeCreators:680,newCreators:68,avgPublish:1.9},{period:"4周前",activeCreators:720,newCreators:72,avgPublish:2}]),te=d([{id:1,title:"红烧肉的经典做法",type:"recipe",categoryName:"家常菜",coverImage:"/recipe-1.jpg",publishTime:"2024-01-20T10:30:00",author:{id:1,username:"chef001",nickname:"美食大师",avatar:""},viewCount:15420,likeCount:856,commentCount:125,shareCount:68},{id:2,title:"营养早餐搭配指南",type:"article",categoryName:"健康饮食",coverImage:"/article-1.jpg",publishTime:"2024-01-19T08:15:00",author:{id:2,username:"nutrition_pro",nickname:"营养师小王",avatar:""},viewCount:12380,likeCount:654,commentCount:89,shareCount:45},{id:3,title:"简单易学的蛋糕制作",type:"video",categoryName:"烘焙",coverImage:"/video-1.jpg",publishTime:"2024-01-18T14:20:00",author:{id:3,username:"baker_master",nickname:"烘焙达人",avatar:""},viewCount:9856,likeCount:445,commentCount:67,shareCount:32}]);De(()=>{z(),Pe(()=>{se()})}),Ae(()=>{_==null||_.dispose(),v==null||v.dispose(),g==null||g.dispose(),h==null||h.dispose(),C==null||C.dispose()});const z=async()=>{D.value=!0;try{await new Promise(n=>setTimeout(n,1e3))}catch{b.error("加载数据失败")}finally{D.value=!1}},ae=()=>{z(),F()},le=()=>{z(),F()},oe=()=>{z(),F()},ne=async()=>{P.value=!0;try{await new Promise(n=>setTimeout(n,2e3)),b.success("数据导出成功")}catch{b.error("导出失败")}finally{P.value=!1}},se=()=>{re(),ie(),de(),ue(),ce()},F=()=>{j(),G(),q(),K(),W()},re=()=>{M.value&&(_=S(M.value),j())},j=()=>{if(!_)return;const n={tooltip:{trigger:"axis"},legend:{data:R.value?["累积内容"]:["新增内容"]},grid:{left:10,right:10,top:50,bottom:20,containLabel:!0},xAxis:{type:"category",data:["01-14","01-15","01-16","01-17","01-18","01-19","01-20"]},yAxis:{type:"value"},series:R.value?[{name:"累积内容",type:"line",smooth:!0,data:[3e3,3045,3083,3148,3196,3268,3426],lineStyle:{color:"#409eff"},areaStyle:{color:new Qe(0,0,0,1,[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}])}}]:[{name:"新增内容",type:"bar",data:[45,38,65,48,72,158],itemStyle:{color:"#409eff"}}]};_.setOption(n)},ie=()=>{N.value&&(v=S(N.value),G())},G=()=>{if(!v)return;const n={tooltip:{trigger:"axis"},legend:{data:["浏览量","点赞数","评论数","分享数"]},grid:{left:10,right:10,top:50,bottom:20,containLabel:!0},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:[{type:"value",name:"浏览量",position:"left"},{type:"value",name:"互动数",position:"right"}],series:[{name:"浏览量",type:"line",yAxisIndex:0,data:[12e3,13500,11800,16500,14800,17200,15800],lineStyle:{color:"#409eff"}},{name:"点赞数",type:"bar",yAxisIndex:1,data:[820,932,901,934,1290,1330,1320],itemStyle:{color:"#67c23a"}},{name:"评论数",type:"bar",yAxisIndex:1,data:[120,150,130,180,200,220,190],itemStyle:{color:"#e6a23c"}},{name:"分享数",type:"bar",yAxisIndex:1,data:[45,52,38,65,72,80,68],itemStyle:{color:"#f56c6c"}}]};v.setOption(n)},de=()=>{O.value&&(g=S(O.value),q())},q=()=>{if(!g)return;const n={tooltip:{trigger:"item"},series:[{type:"pie",radius:"70%",data:[{value:1048,name:"家常菜"},{value:735,name:"甜品"},{value:580,name:"汤品"},{value:484,name:"素食"},{value:300,name:"其他"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};g.setOption(n)},ue=()=>{L.value&&(h=S(L.value),K())},K=()=>{if(!h)return;const n={tooltip:{trigger:"axis"},grid:{left:10,right:10,top:20,bottom:20,containLabel:!0},xAxis:{type:"category",data:["优秀","良好","一般","待改进"]},yAxis:{type:"value"},series:[{type:"bar",data:[580,1250,890,345],itemStyle:{color:function(l){return["#67c23a","#409eff","#e6a23c","#f56c6c"][l.dataIndex]}}}]};h.setOption(n)},ce=()=>{B.value&&(C=S(B.value),W())},W=()=>{if(!C)return;const n={series:[{type:"wordCloud",shape:"circle",left:"center",top:"center",width:"70%",height:"80%",right:null,bottom:null,sizeRange:[12,24],rotationRange:[-90,90],rotationStep:45,gridSize:8,drawOutOfBound:!1,textStyle:{fontFamily:"sans-serif",fontWeight:"bold",color:function(){const l=["#409eff","#67c23a","#e6a23c","#f56c6c","#909399"];return l[Math.floor(Math.random()*l.length)]}},data:[{name:"美味",value:1e3},{name:"简单",value:800},{name:"营养",value:700},{name:"健康",value:600},{name:"家常",value:500},{name:"下饭",value:400},{name:"快手",value:350},{name:"素食",value:300},{name:"汤品",value:250},{name:"甜品",value:200}]}]};C.setOption(n)},pe=()=>{console.log("更新排行榜:",A.value)},fe=n=>{H.push(`/admin/content/recipes/${n}`)},me=n=>{b.info(`推广内容: ${n.title}`)},_e=()=>{b.info("查看内容表现详情")},ve=()=>{b.info("查看创作者详情")},c=n=>n>=1e6?(n/1e6).toFixed(1)+"M":n>=1e3?(n/1e3).toFixed(1)+"K":n.toString(),ge=n=>new Date(n).toLocaleDateString("zh-CN"),he=n=>({recipe:"success",article:"primary",video:"warning"})[n]||"info",Ce=n=>({recipe:"食谱",article:"文章",video:"视频"})[n]||n,ye=n=>((n.likeCount+n.commentCount+n.shareCount)/n.viewCount*100).toFixed(1);return(n,l)=>{const p=u("el-option"),X=u("el-select"),k=u("el-icon"),y=u("el-button"),f=u("el-card"),m=u("el-col"),x=u("el-row"),be=u("el-switch"),i=u("el-table-column"),U=u("el-table"),V=u("el-radio-button"),we=u("el-radio-group"),ke=u("el-tag"),xe=u("el-avatar");return w(),Z("div",qe,[o("div",Ke,[l[6]||(l[6]=o("h2",null,"内容分析",-1)),l[7]||(l[7]=o("p",null,"深度分析内容发布和互动数据",-1)),o("div",We,[e(X,{modelValue:$.value,"onUpdate:modelValue":l[0]||(l[0]=a=>$.value=a),placeholder:"内容类型",onChange:le},{default:t(()=>[e(p,{label:"全部内容",value:"all"}),e(p,{label:"食谱",value:"recipe"}),e(p,{label:"文章",value:"article"}),e(p,{label:"视频",value:"video"})]),_:1},8,["modelValue"]),e(X,{modelValue:Q.value,"onUpdate:modelValue":l[1]||(l[1]=a=>Q.value=a),placeholder:"时间范围",onChange:oe},{default:t(()=>[e(p,{label:"最近7天",value:"7days"}),e(p,{label:"最近30天",value:"30days"}),e(p,{label:"最近90天",value:"90days"}),e(p,{label:"最近半年",value:"6months"})]),_:1},8,["modelValue"]),e(y,{onClick:ne,loading:P.value},{default:t(()=>[e(k,null,{default:t(()=>[e(T(Me))]),_:1}),l[4]||(l[4]=s(" 导出报告 "))]),_:1,__:[4]},8,["loading"]),e(y,{type:"primary",onClick:ae,loading:D.value},{default:t(()=>[e(k,null,{default:t(()=>[e(T(Ne))]),_:1}),l[5]||(l[5]=s(" 刷新 "))]),_:1,__:[5]},8,["loading"])])]),e(x,{gutter:20,class:"metrics-section"},{default:t(()=>[(w(!0),Z(Oe,null,Le(J.value,a=>(w(),I(m,{xs:24,sm:12,lg:6,key:a.key},{default:t(()=>[e(f,{class:"metric-card",shadow:"hover"},{default:t(()=>[o("div",Xe,[o("div",{class:"metric-icon",style:je({background:a.bgColor})},[e(k,{size:28,color:a.iconColor},{default:t(()=>[(w(),I(Ue(a.icon)))]),_:2},1032,["color"])],4),o("div",Ze,[o("div",He,r(c(a.value)),1),o("div",Je,r(a.label),1),o("div",Ye,[e(k,{class:E(a.trend>0?"trend-up":"trend-down")},{default:t(()=>[a.trend>0?(w(),I(T(Ee),{key:0})):(w(),I(T($e),{key:1}))]),_:2},1032,["class"]),o("span",{class:E(a.trend>0?"trend-up":"trend-down")},r(a.trend>0?"+":"")+r(a.trend)+"% ",3)])])])]),_:2},1024)]),_:2},1024))),128))]),_:1}),e(x,{gutter:20,class:"charts-section"},{default:t(()=>[e(m,{xs:24,lg:12},{default:t(()=>[e(f,{class:"chart-card"},{header:t(()=>[o("div",et,[l[8]||(l[8]=o("span",null,"内容发布趋势",-1)),e(be,{modelValue:R.value,"onUpdate:modelValue":l[2]||(l[2]=a=>R.value=a),"active-text":"累积","inactive-text":"新增",onChange:j},null,8,["modelValue"])])]),default:t(()=>[o("div",{class:"chart-container",ref_key:"publishChartRef",ref:M},null,512)]),_:1})]),_:1}),e(m,{xs:24,lg:12},{default:t(()=>[e(f,{class:"chart-card"},{header:t(()=>l[9]||(l[9]=[o("span",null,"内容互动分析",-1)])),default:t(()=>[o("div",{class:"chart-container",ref_key:"interactionChartRef",ref:N},null,512)]),_:1})]),_:1})]),_:1}),e(x,{gutter:20,class:"charts-section"},{default:t(()=>[e(m,{xs:24,lg:8},{default:t(()=>[e(f,{class:"chart-card"},{header:t(()=>l[10]||(l[10]=[o("span",null,"内容分类分布",-1)])),default:t(()=>[o("div",{class:"chart-container",ref_key:"categoryChartRef",ref:O},null,512)]),_:1})]),_:1}),e(m,{xs:24,lg:8},{default:t(()=>[e(f,{class:"chart-card"},{header:t(()=>l[11]||(l[11]=[o("span",null,"内容质量分析",-1)])),default:t(()=>[o("div",{class:"chart-container",ref_key:"qualityChartRef",ref:L},null,512)]),_:1})]),_:1}),e(m,{xs:24,lg:8},{default:t(()=>[e(f,{class:"chart-card"},{header:t(()=>l[12]||(l[12]=[o("span",null,"热门标签云",-1)])),default:t(()=>[o("div",{class:"chart-container",ref_key:"tagCloudChartRef",ref:B},null,512)]),_:1})]),_:1})]),_:1}),e(x,{gutter:20,class:"table-section"},{default:t(()=>[e(m,{xs:24,lg:12},{default:t(()=>[e(f,{class:"table-card"},{header:t(()=>[o("div",tt,[l[14]||(l[14]=o("span",null,"内容表现分析",-1)),e(y,{size:"small",onClick:_e},{default:t(()=>l[13]||(l[13]=[s("查看详情")])),_:1,__:[13]})])]),default:t(()=>[e(U,{data:Y.value,size:"small",height:"300"},{default:t(()=>[e(i,{prop:"metric",label:"指标",width:"120"}),e(i,{prop:"avg",label:"平均值",width:"80"},{default:t(({row:a})=>[s(r(c(a.avg)),1)]),_:1}),e(i,{prop:"median",label:"中位数",width:"80"},{default:t(({row:a})=>[s(r(c(a.median)),1)]),_:1}),e(i,{prop:"top10",label:"TOP10平均"},{default:t(({row:a})=>[s(r(c(a.top10)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),e(m,{xs:24,lg:12},{default:t(()=>[e(f,{class:"table-card"},{header:t(()=>[o("div",at,[l[16]||(l[16]=o("span",null,"创作者活跃度",-1)),e(y,{size:"small",onClick:ve},{default:t(()=>l[15]||(l[15]=[s("查看详情")])),_:1,__:[15]})])]),default:t(()=>[e(U,{data:ee.value,size:"small",height:"300"},{default:t(()=>[e(i,{prop:"period",label:"时间周期",width:"100"}),e(i,{prop:"activeCreators",label:"活跃创作者",width:"100"},{default:t(({row:a})=>[s(r(c(a.activeCreators)),1)]),_:1}),e(i,{prop:"newCreators",label:"新创作者",width:"90"},{default:t(({row:a})=>[s(r(c(a.newCreators)),1)]),_:1}),e(i,{prop:"avgPublish",label:"人均发布"},{default:t(({row:a})=>[s(r(a.avgPublish.toFixed(1)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1}),e(x,{gutter:20,class:"ranking-section"},{default:t(()=>[e(m,{span:24},{default:t(()=>[e(f,{class:"ranking-card"},{header:t(()=>[o("div",lt,[l[21]||(l[21]=o("span",null,"热门内容排行榜",-1)),o("div",ot,[e(we,{modelValue:A.value,"onUpdate:modelValue":l[3]||(l[3]=a=>A.value=a),size:"small",onChange:pe},{default:t(()=>[e(V,{label:"views"},{default:t(()=>l[17]||(l[17]=[s("浏览量")])),_:1,__:[17]}),e(V,{label:"likes"},{default:t(()=>l[18]||(l[18]=[s("点赞数")])),_:1,__:[18]}),e(V,{label:"comments"},{default:t(()=>l[19]||(l[19]=[s("评论数")])),_:1,__:[19]}),e(V,{label:"shares"},{default:t(()=>l[20]||(l[20]=[s("分享数")])),_:1,__:[20]})]),_:1},8,["modelValue"])])])]),default:t(()=>[e(U,{data:te.value,size:"small"},{default:t(()=>[e(i,{label:"排名",width:"80"},{default:t(({$index:a})=>[o("div",{class:E(["rank-badge",`rank-${a+1}`])},r(a+1),3)]),_:1}),e(i,{label:"内容信息","min-width":"300"},{default:t(({row:a})=>[o("div",nt,[o("div",st,[o("img",{src:a.coverImage||"/default-recipe.jpg",alt:"封面"},null,8,rt)]),o("div",it,[o("div",dt,r(a.title),1),o("div",ut,[e(ke,{size:"small",type:he(a.type)},{default:t(()=>[s(r(Ce(a.type)),1)]),_:2},1032,["type"]),o("span",ct,r(a.categoryName),1),o("span",pt,r(ge(a.publishTime)),1)])])])]),_:1}),e(i,{label:"作者",width:"120"},{default:t(({row:a})=>[o("div",ft,[e(xe,{size:24,src:a.author.avatar},{default:t(()=>[e(k,null,{default:t(()=>[e(T(Be))]),_:1})]),_:2},1032,["src"]),o("span",null,r(a.author.nickname||a.author.username),1)])]),_:1}),e(i,{prop:"viewCount",label:"浏览量",width:"100"},{default:t(({row:a})=>[s(r(c(a.viewCount)),1)]),_:1}),e(i,{prop:"likeCount",label:"点赞数",width:"100"},{default:t(({row:a})=>[s(r(c(a.likeCount)),1)]),_:1}),e(i,{prop:"commentCount",label:"评论数",width:"100"},{default:t(({row:a})=>[s(r(c(a.commentCount)),1)]),_:1}),e(i,{prop:"shareCount",label:"分享数",width:"100"},{default:t(({row:a})=>[s(r(c(a.shareCount)),1)]),_:1}),e(i,{label:"互动率",width:"100"},{default:t(({row:a})=>[o("span",mt,r(ye(a))+"% ",1)]),_:1}),e(i,{label:"操作",width:"120"},{default:t(({row:a})=>[e(y,{type:"text",size:"small",onClick:Te=>fe(a.id)},{default:t(()=>l[22]||(l[22]=[s(" 查看详情 ")])),_:2,__:[22]},1032,["onClick"]),e(y,{type:"text",size:"small",onClick:Te=>me(a)},{default:t(()=>l[23]||(l[23]=[s(" 推广 ")])),_:2,__:[23]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1})])}}}),yt=Ge(_t,[["__scopeId","data-v-1b1cc9c8"]]);export{yt as default};
