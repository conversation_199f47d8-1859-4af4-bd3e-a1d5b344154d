import{d as me,r as w,a3 as R,o as fe,ag as ye,ah as he,ao as pe,c as x,am as oe,J as D,e as s,t as f,f as l,P as h,j as _,w as t,S as X,T as Z,n as o,h as re,G as be,I as xe,au as we,k as r,av as ie,aw as ke,p as A,K as C,O as J,C as Ce}from"./index-BDkHONMN.js";import{i as Se,m as $e,L as ue}from"./index-BNz9ATsT.js";import{_ as ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ae={key:0,class:"chart-header"},Oe={class:"chart-title"},Be={key:0},Pe={class:"chart-actions"},Ue={key:1,class:"chart-footer"},Le={key:0,class:"chart-legend"},Ie=["onClick"],De={class:"legend-name"},Re={class:"legend-value"},Ee={key:1,class:"chart-summary"},Me={class:"summary-label"},ze={class:"summary-value"},Te=me({__name:"AdvancedChart",props:{type:{default:"line"},data:{},options:{},title:{},subtitle:{},height:{default:"400px"},showHeader:{type:Boolean,default:!0},showFooter:{type:Boolean,default:!1},exportable:{type:Boolean,default:!0},realtime:{type:Boolean,default:!1},realtimeInterval:{default:5e3},customLegend:{type:Boolean,default:!1},summary:{type:Boolean,default:!1},timePeriods:{default:()=>[{label:"今日",value:"today"},{label:"7天",value:"7days"},{label:"30天",value:"30days"},{label:"90天",value:"90days"}]},theme:{default:"default"},responsive:{type:Boolean,default:!0}},emits:["periodChange","dataPointClick","legendClick","export","refresh"],setup(de,{expose:E,emit:T}){var S;const n=de,g=T,O=w(),M=w(!1),H=w(((S=n.timePeriods[0])==null?void 0:S.value)||"today"),F=w(!1);let i=null,B=null;const q=R(()=>n.showHeader&&n.showFooter?"calc(100% - 120px)":n.showHeader||n.showFooter?"calc(100% - 60px)":"100%"),Q=R(()=>i?i.getOption().series.map(d=>{var c,m,$;return{name:d.name,color:((c=d.itemStyle)==null?void 0:c.color)||((m=d.lineStyle)==null?void 0:m.color)||"#409eff",value:(($=d.data)==null?void 0:$.length)||0}}):[]),W=R(()=>{if(!n.data.length)return[];const a=n.data.reduce((m,$)=>m+($.value||0),0),u=a/n.data.length,d=Math.max(...n.data.map(m=>m.value||0)),c=Math.min(...n.data.map(m=>m.value||0));return[{label:"总计",value:a.toLocaleString()},{label:"平均",value:u.toFixed(2)},{label:"最大",value:d.toLocaleString()},{label:"最小",value:c.toLocaleString()}]});fe(()=>{ye(()=>{Y(),n.realtime&&L()})}),he(()=>{i&&i.dispose(),B&&clearInterval(B)}),pe(()=>n.data,()=>{U()},{deep:!0}),pe(()=>n.options,()=>{U()},{deep:!0});const Y=()=>{O.value&&(i=Se(O.value,n.theme),U(),i.on("click",a=>{g("dataPointClick",a)}),i.on("legendselectchanged",a=>{g("legendClick",a.name)}),n.responsive&&window.addEventListener("resize",V))},U=()=>{if(!i)return;const a=ee();i.setOption(a,!0)},ee=()=>{const a={tooltip:{trigger:"axis",backgroundColor:"rgba(50, 50, 50, 0.9)",borderColor:"#333",textStyle:{color:"#fff"},formatter:d=>{if(Array.isArray(d)){let c=`${d[0].axisValue}<br/>`;return d.forEach(m=>{c+=`${m.marker} ${m.seriesName}: ${m.value}<br/>`}),c}return`${d.marker} ${d.name}: ${d.value}`}},legend:{show:!n.customLegend,top:10,textStyle:{color:"#666"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},toolbox:{show:!1},animation:!0,animationDuration:1e3,animationEasing:"cubicOut"};let u={};switch(n.type){case"line":u=te();break;case"bar":u=ae();break;case"pie":u=j();break;case"scatter":u=le();break;case"heatmap":u=se();break;case"radar":u=ne();break;case"gauge":u=p();break;case"funnel":u=e();break;case"treemap":u=I();break;case"sunburst":u=y();break}return $e(a,u,n.options||{})},te=()=>({xAxis:{type:"category",data:n.data.map(a=>a.name||a.x),axisLine:{lineStyle:{color:"#e0e0e0"}}},yAxis:{type:"value",axisLine:{lineStyle:{color:"#e0e0e0"}},splitLine:{lineStyle:{color:"#f0f0f0"}}},series:[{type:"line",data:n.data.map(a=>a.value||a.y),smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:3,color:new ue(0,0,1,0,[{offset:0,color:"#409eff"},{offset:1,color:"#67c23a"}])},areaStyle:{color:new ue(0,0,0,1,[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}])}}]}),ae=()=>({xAxis:{type:"category",data:n.data.map(a=>a.name||a.x)},yAxis:{type:"value"},series:[{type:"bar",data:n.data.map(a=>a.value||a.y),itemStyle:{color:new ue(0,0,0,1,[{offset:0,color:"#409eff"},{offset:1,color:"#67c23a"}]),borderRadius:[4,4,0,0]}}]}),j=()=>({series:[{type:"pie",radius:["40%","70%"],data:n.data,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{formatter:"{b}: {c} ({d}%)"}}]}),le=()=>({xAxis:{type:"value"},yAxis:{type:"value"},series:[{type:"scatter",data:n.data.map(a=>[a.x,a.y]),symbolSize:8,itemStyle:{color:"#409eff",opacity:.7}}]}),se=()=>({xAxis:{type:"category",data:n.data.map(a=>a.x)},yAxis:{type:"category",data:n.data.map(a=>a.y)},visualMap:{min:0,max:Math.max(...n.data.map(a=>a.value)),calculable:!0,orient:"horizontal",left:"center",bottom:"15%"},series:[{type:"heatmap",data:n.data.map(a=>[a.x,a.y,a.value]),label:{show:!0}}]}),ne=()=>({radar:{indicator:n.data.map(a=>({name:a.name,max:a.max||100}))},series:[{type:"radar",data:[{value:n.data.map(a=>a.value),name:"数据"}]}]}),p=()=>({series:[{type:"gauge",data:n.data,detail:{formatter:"{value}%"}}]}),e=()=>({series:[{type:"funnel",data:n.data,left:"10%",top:60,width:"80%",bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",gap:2}]}),I=()=>({series:[{type:"treemap",data:n.data}]}),y=()=>({series:[{type:"sunburst",data:n.data,radius:[0,"90%"],label:{rotate:"radial"}}]}),v=a=>{H.value=a,g("periodChange",a)},k=()=>{M.value=!0,g("refresh"),setTimeout(()=>{M.value=!1,U()},1e3)},K=()=>{F.value=!F.value},z=a=>{i&&i.dispatchAction({type:"legendToggleSelect",name:a})},P=a=>{if(i){switch(a){case"png":case"jpeg":const u=i.getDataURL({type:a,pixelRatio:2,backgroundColor:"#fff"}),d=document.createElement("a");d.download=`chart.${a}`,d.href=u,d.click();break;case"svg":const c=i.renderToSVGString(),m=new Blob([c],{type:"image/svg+xml"}),$=URL.createObjectURL(m),N=document.createElement("a");N.download="chart.svg",N.href=$,N.click(),URL.revokeObjectURL($);break}g("export",a,n.data)}},V=()=>{i&&i.resize()},L=()=>{B&&clearInterval(B),B=setInterval(()=>{g("refresh")},n.realtimeInterval)};return E({refreshChart:k,updateChart:U,startRealtime:L,stopRealtime:()=>{B&&(clearInterval(B),B=null)},getChart:()=>i}),(a,u)=>{const d=_("el-button"),c=_("el-button-group"),m=_("el-icon"),$=_("el-dropdown-item"),N=_("el-dropdown-menu"),_e=_("el-dropdown");return r(),x("div",{class:"advanced-chart",style:oe({height:a.height})},[a.showHeader?(r(),x("div",Ae,[s("div",Oe,[s("h3",null,f(a.title),1),a.subtitle?(r(),x("p",Be,f(a.subtitle),1)):D("",!0)]),s("div",Pe,[l(c,{size:"small"},{default:t(()=>[(r(!0),x(X,null,Z(a.timePeriods,b=>(r(),h(d,{key:b.value,type:H.value===b.value?"primary":"",onClick:ge=>v(b.value)},{default:t(()=>[o(f(b.label),1)]),_:2},1032,["type","onClick"]))),128))]),_:1}),a.exportable?(r(),h(_e,{key:0,onCommand:P},{dropdown:t(()=>[l(N,null,{default:t(()=>[l($,{command:"png"},{default:t(()=>u[1]||(u[1]=[o("PNG图片")])),_:1,__:[1]}),l($,{command:"jpeg"},{default:t(()=>u[2]||(u[2]=[o("JPG图片")])),_:1,__:[2]}),l($,{command:"svg"},{default:t(()=>u[3]||(u[3]=[o("SVG矢量图")])),_:1,__:[3]})]),_:1})]),default:t(()=>[l(d,{size:"small"},{default:t(()=>[l(m,null,{default:t(()=>[l(re(be))]),_:1}),u[0]||(u[0]=o(" 导出 "))]),_:1,__:[0]})]),_:1})):D("",!0),l(d,{size:"small",onClick:k,loading:M.value},{default:t(()=>[l(m,null,{default:t(()=>[l(re(xe))]),_:1})]),_:1},8,["loading"]),l(d,{size:"small",onClick:K},{default:t(()=>[l(m,null,{default:t(()=>[l(re(we))]),_:1})]),_:1})])])):D("",!0),s("div",{class:"chart-container",ref_key:"chartRef",ref:O,style:oe({height:q.value})},null,4),a.showFooter?(r(),x("div",Ue,[a.customLegend?(r(),x("div",Le,[(r(!0),x(X,null,Z(Q.value,b=>(r(),x("div",{key:b.name,class:"legend-item",onClick:ge=>z(b.name)},[s("span",{class:"legend-color",style:oe({backgroundColor:b.color})},null,4),s("span",De,f(b.name),1),s("span",Re,f(b.value),1)],8,Ie))),128))])):D("",!0),a.summary?(r(),x("div",Ee,[(r(!0),x(X,null,Z(W.value,b=>(r(),x("div",{class:"summary-item",key:b.label},[s("span",Me,f(b.label)+":",1),s("span",ze,f(b.value),1)]))),128))])):D("",!0)])):D("",!0)],4)}}}),G=ve(Te,[["__scopeId","data-v-efd65d79"]]),Fe={class:"advanced-features"},Ve={class:"demo-container"},Ne={class:"card-header"},Ge={class:"permission-demo"},He={class:"demo-buttons"},je={class:"demo-buttons"},Ke={class:"demo-buttons"},Je={class:"permission-info"},Xe={class:"card-header"},Ze={class:"charts-demo"},qe={class:"card-header"},Qe={class:"api-demo"},We={key:0,class:"stats-grid"},Ye={class:"stat-item"},et={class:"stat-value"},tt={class:"stat-item"},at={class:"stat-value"},lt={class:"stat-item"},st={class:"stat-value"},nt={class:"stat-item"},ot={class:"stat-value"},rt={class:"card-header"},it={class:"env-demo"},ut=me({__name:"AdvancedFeatures",setup(de){const E=w(!1),T=w(!1),n=w(!1),g=w([]),O=w(null),M=R(()=>ie.getCurrentUser()),H=R(()=>ie.getPermissions()),F=R(()=>ie.isSuperAdmin()),i=R(()=>ke),B=w([{name:"1月",value:120},{name:"2月",value:200},{name:"3月",value:150},{name:"4月",value:300},{name:"5月",value:250},{name:"6月",value:400}]),q=w([{name:"北京",value:335},{name:"上海",value:310},{name:"广州",value:234},{name:"深圳",value:135},{name:"其他",value:1548}]),Q=w([{name:"CPU使用率",value:75}]),W=w([{name:"用户管理",value:85,max:100},{name:"内容管理",value:90,max:100},{name:"数据统计",value:70,max:100},{name:"系统设置",value:80,max:100},{name:"权限管理",value:95,max:100}]),Y=w([{name:"访问",value:1e3},{name:"注册",value:800},{name:"激活",value:600},{name:"付费",value:300}]);fe(()=>{setTimeout(()=>{U(),j()},1e3)});const U=async()=>{E.value=!0;try{await new Promise(p=>setTimeout(p,1e3)),g.value=[{id:1,username:"admin",email:"<EMAIL>",status:1},{id:2,username:"user1",email:"<EMAIL>",status:1},{id:3,username:"user2",email:"<EMAIL>",status:0}],A.success("用户列表获取成功")}catch{A.error("获取用户列表失败")}finally{E.value=!1}},ee=async()=>{T.value=!0;try{await new Promise(e=>setTimeout(e,1e3));const p={id:g.value.length+1,username:`user${g.value.length+1}`,email:`user${g.value.length+1}@example.com`,status:1};g.value.push(p),A.success("用户创建成功")}catch{A.error("创建用户失败")}finally{T.value=!1}},te=p=>{A.info(`编辑用户: ${p.username}`)},ae=async p=>{try{await Ce.confirm(`确定要删除用户 ${p.username} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=g.value.findIndex(I=>I.id===p.id);e>-1&&(g.value.splice(e,1),A.success("用户删除成功"))}catch{A.info("已取消删除")}},j=async()=>{n.value=!0;try{await new Promise(p=>setTimeout(p,1e3)),O.value={totalUsers:1234,totalRecipes:5678,todayVisits:890,activeUsers:456},A.success("统计数据获取成功")}catch{A.error("获取统计数据失败")}finally{n.value=!1}},le=p=>{A.info(`切换时间周期: ${p}`)},se=()=>{A.info("刷新图表数据")},ne=p=>{if(p===0)return"0 B";const e=1024,I=["B","KB","MB","GB"],y=Math.floor(Math.log(p)/Math.log(e));return parseFloat((p/Math.pow(e,y)).toFixed(2))+" "+I[y]};return(p,e)=>{const I=_("el-page-header"),y=_("el-tag"),v=_("el-button"),k=_("el-descriptions-item"),K=_("el-descriptions"),z=_("el-card"),P=_("el-col"),V=_("el-row"),L=_("el-table-column"),ce=_("el-table"),S=J("permission"),a=J("role"),u=J("super-admin"),d=J("loading");return r(),x("div",Fe,[l(I,{onBack:e[0]||(e[0]=c=>p.$router.go(-1)),content:"高级功能演示"}),s("div",Ve,[l(z,{class:"demo-card"},{header:t(()=>[s("div",Ne,[e[2]||(e[2]=s("span",null,"权限控制演示",-1)),l(y,{type:"info"},{default:t(()=>e[1]||(e[1]=[o("Permission Control")])),_:1,__:[1]})])]),default:t(()=>[s("div",Ge,[e[12]||(e[12]=s("h4",null,"基础权限控制",-1)),s("div",He,[C((r(),h(v,{type:"primary"},{default:t(()=>e[3]||(e[3]=[o(" 创建用户 (需要 user:create 权限) ")])),_:1,__:[3]})),[[S,"user:create"]]),C((r(),h(v,{type:"danger"},{default:t(()=>e[4]||(e[4]=[o(" 删除用户 (需要 user:delete 权限) ")])),_:1,__:[4]})),[[S,"user:delete"]]),C((r(),h(v,{type:"success"},{default:t(()=>e[5]||(e[5]=[o(" 导出用户 (禁用模式) ")])),_:1,__:[5]})),[[S,"user:export",void 0,{disable:!0}]])]),e[13]||(e[13]=s("h4",null,"角色权限控制",-1)),s("div",je,[C((r(),h(v,{type:"primary"},{default:t(()=>e[6]||(e[6]=[o(" 管理员功能 ")])),_:1,__:[6]})),[[a,"ADMIN"]]),C((r(),h(v,{type:"warning"},{default:t(()=>e[7]||(e[7]=[o(" 管理员或版主功能 ")])),_:1,__:[7]})),[[a,["ADMIN","MODERATOR"],void 0,{any:!0}]]),C((r(),h(v,{type:"danger"},{default:t(()=>e[8]||(e[8]=[o(" 超级管理员功能 ")])),_:1,__:[8]})),[[u]])]),e[14]||(e[14]=s("h4",null,"资源权限控制",-1)),s("div",Ke,[C((r(),h(v,{type:"primary"},{default:t(()=>e[9]||(e[9]=[o(" 创建食谱 ")])),_:1,__:[9]})),[[S,"recipe:create","resource"]]),C((r(),h(v,{type:"warning"},{default:t(()=>e[10]||(e[10]=[o(" 审核食谱 ")])),_:1,__:[10]})),[[S,"recipe:audit","resource"]]),C((r(),h(v,{type:"success"},{default:t(()=>e[11]||(e[11]=[o(" 导出食谱 ")])),_:1,__:[11]})),[[S,"recipe_export","button"]])]),s("div",Je,[l(K,{title:"当前用户权限信息",column:2,border:""},{default:t(()=>[l(k,{label:"用户名"},{default:t(()=>{var c;return[o(f(((c=M.value)==null?void 0:c.username)||"未登录"),1)]}),_:1}),l(k,{label:"角色"},{default:t(()=>{var c;return[(r(!0),x(X,null,Z((c=M.value)==null?void 0:c.roles,m=>(r(),h(y,{key:m.code,type:"primary"},{default:t(()=>[o(f(m.name),1)]),_:2},1024))),128))]}),_:1}),l(k,{label:"权限数量"},{default:t(()=>[o(f(H.value.length),1)]),_:1}),l(k,{label:"是否超级管理员"},{default:t(()=>[l(y,{type:F.value?"success":"info"},{default:t(()=>[o(f(F.value?"是":"否"),1)]),_:1},8,["type"])]),_:1})]),_:1})])])]),_:1}),l(z,{class:"demo-card"},{header:t(()=>[s("div",Xe,[e[16]||(e[16]=s("span",null,"高级图表演示",-1)),l(y,{type:"success"},{default:t(()=>e[15]||(e[15]=[o("Advanced Charts")])),_:1,__:[15]})])]),default:t(()=>[s("div",Ze,[l(V,{gutter:20},{default:t(()=>[l(P,{span:12},{default:t(()=>[l(G,{type:"line",title:"用户增长趋势",subtitle:"最近30天用户注册数据",data:B.value,"show-footer":!0,summary:!0,exportable:!0,realtime:!1,height:"300px",onPeriodChange:le,onRefresh:se},null,8,["data"])]),_:1}),l(P,{span:12},{default:t(()=>[l(G,{type:"pie",title:"用户分布",subtitle:"按地区统计",data:q.value,"show-footer":!0,"custom-legend":!0,height:"300px"},null,8,["data"])]),_:1})]),_:1}),l(V,{gutter:20,style:{"margin-top":"20px"}},{default:t(()=>[l(P,{span:8},{default:t(()=>[l(G,{type:"gauge",title:"系统性能",data:Q.value,height:"250px","show-header":!1},null,8,["data"])]),_:1}),l(P,{span:8},{default:t(()=>[l(G,{type:"radar",title:"功能使用率",data:W.value,height:"250px","show-header":!1},null,8,["data"])]),_:1}),l(P,{span:8},{default:t(()=>[l(G,{type:"funnel",title:"用户转化漏斗",data:Y.value,height:"250px","show-header":!1},null,8,["data"])]),_:1})]),_:1})])]),_:1}),l(z,{class:"demo-card"},{header:t(()=>[s("div",qe,[e[18]||(e[18]=s("span",null,"API集成演示",-1)),l(y,{type:"warning"},{default:t(()=>e[17]||(e[17]=[o("API Integration")])),_:1,__:[17]})])]),default:t(()=>[s("div",Qe,[l(V,{gutter:20},{default:t(()=>[l(P,{span:12},{default:t(()=>[e[23]||(e[23]=s("h4",null,"用户数据",-1)),l(v,{onClick:U,loading:E.value,type:"primary"},{default:t(()=>e[19]||(e[19]=[o(" 获取用户列表 ")])),_:1,__:[19]},8,["loading"]),l(v,{onClick:ee,loading:T.value,type:"success"},{default:t(()=>e[20]||(e[20]=[o(" 创建用户 ")])),_:1,__:[20]},8,["loading"]),C((r(),h(ce,{data:g.value,style:{"margin-top":"10px"},"max-height":"300"},{default:t(()=>[l(L,{prop:"id",label:"ID",width:"60"}),l(L,{prop:"username",label:"用户名"}),l(L,{prop:"email",label:"邮箱"}),l(L,{prop:"status",label:"状态"},{default:t(({row:c})=>[l(y,{type:c.status===1?"success":"danger"},{default:t(()=>[o(f(c.status===1?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),l(L,{label:"操作",width:"120"},{default:t(({row:c})=>[C((r(),h(v,{size:"small",onClick:m=>te(c)},{default:t(()=>e[21]||(e[21]=[o(" 编辑 ")])),_:2,__:[21]},1032,["onClick"])),[[S,"user:update"]]),C((r(),h(v,{size:"small",type:"danger",onClick:m=>ae(c)},{default:t(()=>e[22]||(e[22]=[o(" 删除 ")])),_:2,__:[22]},1032,["onClick"])),[[S,"user:delete"]])]),_:1})]),_:1},8,["data"])),[[d,E.value]])]),_:1,__:[23]}),l(P,{span:12},{default:t(()=>[e[29]||(e[29]=s("h4",null,"系统统计",-1)),l(v,{onClick:j,loading:n.value,type:"primary"},{default:t(()=>e[24]||(e[24]=[o(" 获取统计数据 ")])),_:1,__:[24]},8,["loading"]),O.value?(r(),x("div",We,[s("div",Ye,[s("div",et,f(O.value.totalUsers),1),e[25]||(e[25]=s("div",{class:"stat-label"},"总用户数",-1))]),s("div",tt,[s("div",at,f(O.value.totalRecipes),1),e[26]||(e[26]=s("div",{class:"stat-label"},"总食谱数",-1))]),s("div",lt,[s("div",st,f(O.value.todayVisits),1),e[27]||(e[27]=s("div",{class:"stat-label"},"今日访问",-1))]),s("div",nt,[s("div",ot,f(O.value.activeUsers),1),e[28]||(e[28]=s("div",{class:"stat-label"},"活跃用户",-1))])])):D("",!0)]),_:1,__:[29]})]),_:1})])]),_:1}),l(z,{class:"demo-card"},{header:t(()=>[s("div",rt,[e[31]||(e[31]=s("span",null,"环境信息",-1)),l(y,{type:"info"},{default:t(()=>e[30]||(e[30]=[o("Environment Info")])),_:1,__:[30]})])]),default:t(()=>[s("div",it,[l(K,{title:"环境配置",column:3,border:""},{default:t(()=>[l(k,{label:"环境模式"},{default:t(()=>[l(y,{type:i.value.IS_DEV?"warning":"success"},{default:t(()=>[o(f(i.value.NODE_ENV),1)]),_:1},8,["type"])]),_:1}),l(k,{label:"API地址"},{default:t(()=>[o(f(i.value.API_BASE_URL),1)]),_:1}),l(k,{label:"应用版本"},{default:t(()=>[o(f(i.value.APP_VERSION),1)]),_:1}),l(k,{label:"调试模式"},{default:t(()=>[l(y,{type:i.value.DEBUG?"warning":"info"},{default:t(()=>[o(f(i.value.DEBUG?"开启":"关闭"),1)]),_:1},8,["type"])]),_:1}),l(k,{label:"Mock模式"},{default:t(()=>[l(y,{type:i.value.MOCK?"warning":"info"},{default:t(()=>[o(f(i.value.MOCK?"开启":"关闭"),1)]),_:1},8,["type"])]),_:1}),l(k,{label:"上传限制"},{default:t(()=>[o(f(ne(i.value.UPLOAD_MAX_SIZE)),1)]),_:1})]),_:1})])]),_:1})])])}}}),mt=ve(ut,[["__scopeId","data-v-0f947ac7"]]);export{mt as default};
