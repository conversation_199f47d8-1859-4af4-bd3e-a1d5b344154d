import{d as we,r as $,a as J,o as Re,p as r,c as y,e as s,f as t,w as l,j as u,g as Ve,S as D,T,n as i,h as f,H as ze,I as Se,J as V,K as $e,t as o,L as xe,G as Q,B as Be,a0 as Me,N as q,O as De,P as z,Q as Te,D as Ie,s as Ue,U as Ne,a1 as Ye,C as x,b as Fe,k as d}from"./index-BDkHONMN.js";import{_ as Le}from"./_plugin-vue_export-helper-DlAUqK2U.js";const je={class:"recipe-list-page"},Ae={key:0,class:"batch-toolbar"},Ee={class:"selected-text"},Ke={class:"batch-actions"},Pe={class:"recipe-info"},Oe={class:"recipe-cover"},Ge=["src"],He={class:"recipe-badges"},Je={class:"recipe-details"},Qe={class:"recipe-title"},qe={class:"recipe-meta"},We={class:"category"},Xe={class:"difficulty"},Ze={class:"cooking-time"},et={class:"recipe-desc"},tt={class:"recipe-tags"},at={key:0,class:"more-tags"},lt={class:"author-info"},st={class:"author-details"},nt={class:"author-name"},ot={class:"author-username"},it={class:"stats-info"},dt={class:"recipe-data"},ut={class:"time-info"},rt={class:"update-time"},ct={class:"actions"},pt={class:"pagination-wrapper"},mt={key:0,class:"recipe-preview"},_t={class:"preview-header"},ft=["src"],vt={class:"preview-info"},gt={class:"preview-meta"},yt={class:"preview-tags"},ht={class:"preview-content"},bt={class:"ingredients-section"},kt={class:"ingredients-list"},Ct={class:"ingredient-name"},wt={class:"ingredient-amount"},Rt={class:"steps-section"},Vt={class:"steps-list"},zt={class:"step-number"},St={class:"step-content"},$t=["src"],xt={class:"dialog-footer"},Bt=we({__name:"RecipeList",setup(Mt){const W=Fe(),I=$(!1),Y=$([]),S=$([]),U=$(!1),c=$(null),L=$([]),p=J({keyword:"",categoryId:null,status:null,difficulty:null,isRecommended:null,dateRange:[]}),b=J({page:1,pageSize:20,total:0});Re(()=>{X(),g()});const X=async()=>{try{L.value=[{id:1,name:"家常菜"},{id:2,name:"甜品"},{id:3,name:"汤品"},{id:4,name:"素食"}]}catch{r.error("加载分类失败")}},g=async()=>{I.value=!0;try{Y.value=[{id:1,title:"红烧肉的做法",description:"正宗的红烧肉制作方法，香甜可口，肥而不腻",coverImage:"/recipe-1.jpg",categoryId:1,categoryName:"家常菜",difficulty:"medium",cookingTime:60,servings:4,status:1,isRecommended:!0,isFeatured:!1,tags:["红烧","肉类","下饭菜"],author:{id:1,username:"chef001",nickname:"美食达人",avatar:""},viewCount:1250,likeCount:89,favoriteCount:45,commentCount:23,ingredientCount:8,stepCount:6,ingredients:[{name:"五花肉",amount:"500",unit:"克"},{name:"生抽",amount:"2",unit:"勺"},{name:"老抽",amount:"1",unit:"勺"}],steps:[{description:"将五花肉切成块状，用开水焯水去血沫"},{description:"热锅下油，放入肉块煎至两面金黄"}],createdAt:"2024-01-20T10:30:00",updatedAt:"2024-01-20T14:20:00"}],b.total=1}catch{r.error("加载食谱列表失败")}finally{I.value=!1}},F=()=>{b.page=1,g()},Z=()=>{Object.assign(p,{keyword:"",categoryId:null,status:null,difficulty:null,isRecommended:null,dateRange:[]}),F()},ee=n=>{S.value=n},j=async n=>{if(S.value.length===0){r.warning("请选择要操作的食谱");return}const e=n===1?"发布":"下架";try{await x.confirm(`确定要${e}选中的食谱吗？`,"提示",{type:"warning"}),r.success(`${e}成功`),g()}catch{}},A=async n=>{if(S.value.length===0){r.warning("请选择要操作的食谱");return}const e=n?"推荐":"取消推荐";try{await x.confirm(`确定要${e}选中的食谱吗？`,"提示",{type:"warning"}),r.success(`${e}成功`),g()}catch{}},te=async()=>{if(S.value.length===0){r.warning("请选择要删除的食谱");return}try{await x.confirm("确定要删除选中的食谱吗？此操作不可逆！","危险操作",{type:"error",confirmButtonText:"确认删除",confirmButtonClass:"el-button--danger"}),r.success("删除成功"),g()}catch{}},ae=async n=>{const{action:e,recipe:v}=n;switch(e){case"edit":E(v.id);break;case"toggleStatus":await le(v);break;case"toggleRecommend":await se(v);break;case"feature":await ne(v);break;case"duplicate":await oe();break;case"export":await ie();break;case"delete":await de();break}},le=async n=>{const e=n.status===1?"下架":"发布";try{await x.confirm(`确定要${e}此食谱吗？`,"确认操作",{type:"warning"}),r.success(`${e}成功`),g()}catch{}},se=async n=>{const e=n.isRecommended?"取消推荐":"推荐";try{r.success(`${e}成功`),g()}catch{r.error(`${e}失败`)}},ne=async n=>{const e=n.isFeatured?"取消精选":"设为精选";try{r.success(`${e}成功`),g()}catch{r.error(`${e}失败`)}},oe=async n=>{try{await x.confirm("确定要复制此食谱吗？","确认操作",{type:"info"}),r.success("复制成功"),g()}catch{}},ie=async n=>{try{r.success("导出成功")}catch{r.error("导出失败")}},de=async n=>{try{await x.confirm("确定要删除此食谱吗？此操作将永久删除食谱及其所有数据，且不可逆！","危险操作",{type:"error",confirmButtonText:"确认删除",confirmButtonClass:"el-button--danger"}),r.success("删除成功"),g()}catch{}},ue=n=>{const e=Y.value.find(v=>v.id===n);e&&(c.value=e,U.value=!0)},E=n=>{W.push(`/admin/content/recipes/${n}/edit`)},re=n=>{b.page=n,g()},ce=n=>{b.pageSize=n,b.page=1,g()},pe=n=>({0:"warning",1:"success",2:"danger",3:"info"})[n]||"info",me=n=>({0:"待审核",1:"已发布",2:"已拒绝",3:"已下架"})[n]||"未知",K=n=>({easy:"简单",medium:"中等",hard:"困难"})[n]||n,P=n=>new Date(n).toLocaleString("zh-CN");return(n,e)=>{var G;const v=u("el-input"),w=u("el-form-item"),m=u("el-option"),N=u("el-select"),_e=u("el-date-picker"),_=u("el-icon"),h=u("el-button"),fe=u("el-form"),O=u("el-card"),k=u("el-table-column"),B=u("el-tag"),ve=u("el-avatar"),R=u("el-dropdown-item"),ge=u("el-dropdown-menu"),ye=u("el-dropdown"),he=u("el-table"),be=u("el-pagination"),ke=u("el-dialog"),Ce=De("loading");return d(),y("div",je,[e[34]||(e[34]=s("div",{class:"page-header"},[s("h2",null,"食谱管理"),s("p",null,"管理系统中的所有食谱内容")],-1)),t(O,{class:"toolbar-card"},{default:l(()=>[t(fe,{model:p,inline:!0,class:"search-form"},{default:l(()=>[t(w,{label:"关键词"},{default:l(()=>[t(v,{modelValue:p.keyword,"onUpdate:modelValue":e[0]||(e[0]=a=>p.keyword=a),placeholder:"搜索食谱名称、描述、作者",clearable:"",style:{width:"250px"},onKeyup:Ve(F,["enter"])},null,8,["modelValue"])]),_:1}),t(w,{label:"分类"},{default:l(()=>[t(N,{modelValue:p.categoryId,"onUpdate:modelValue":e[1]||(e[1]=a=>p.categoryId=a),placeholder:"选择分类",clearable:""},{default:l(()=>[t(m,{label:"全部分类",value:null}),(d(!0),y(D,null,T(L.value,a=>(d(),z(m,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"状态"},{default:l(()=>[t(N,{modelValue:p.status,"onUpdate:modelValue":e[2]||(e[2]=a=>p.status=a),placeholder:"选择状态",clearable:""},{default:l(()=>[t(m,{label:"全部状态",value:null}),t(m,{label:"待审核",value:0}),t(m,{label:"已发布",value:1}),t(m,{label:"已拒绝",value:2}),t(m,{label:"已下架",value:3})]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"难度"},{default:l(()=>[t(N,{modelValue:p.difficulty,"onUpdate:modelValue":e[3]||(e[3]=a=>p.difficulty=a),placeholder:"选择难度",clearable:""},{default:l(()=>[t(m,{label:"全部难度",value:null}),t(m,{label:"简单",value:"easy"}),t(m,{label:"中等",value:"medium"}),t(m,{label:"困难",value:"hard"})]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"推荐状态"},{default:l(()=>[t(N,{modelValue:p.isRecommended,"onUpdate:modelValue":e[4]||(e[4]=a=>p.isRecommended=a),placeholder:"推荐状态",clearable:""},{default:l(()=>[t(m,{label:"全部",value:null}),t(m,{label:"已推荐",value:!0}),t(m,{label:"未推荐",value:!1})]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"创建时间"},{default:l(()=>[t(_e,{modelValue:p.dateRange,"onUpdate:modelValue":e[5]||(e[5]=a=>p.dateRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),t(w,null,{default:l(()=>[t(h,{type:"primary",onClick:F,loading:I.value},{default:l(()=>[t(_,null,{default:l(()=>[t(f(ze))]),_:1}),e[15]||(e[15]=i(" 搜索 "))]),_:1,__:[15]},8,["loading"]),t(h,{onClick:Z},{default:l(()=>[t(_,null,{default:l(()=>[t(f(Se))]),_:1}),e[16]||(e[16]=i(" 重置 "))]),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),_:1}),t(O,{class:"table-card"},{default:l(()=>[S.value.length>0?(d(),y("div",Ae,[s("span",Ee,"已选择 "+o(S.value.length)+" 个食谱",1),s("div",Ke,[t(h,{type:"success",size:"small",onClick:e[6]||(e[6]=a=>j(1))},{default:l(()=>[t(_,null,{default:l(()=>[t(f(xe))]),_:1}),e[17]||(e[17]=i(" 批量发布 "))]),_:1,__:[17]}),t(h,{type:"warning",size:"small",onClick:e[7]||(e[7]=a=>j(3))},{default:l(()=>[t(_,null,{default:l(()=>[t(f(Q))]),_:1}),e[18]||(e[18]=i(" 批量下架 "))]),_:1,__:[18]}),t(h,{type:"primary",size:"small",onClick:e[8]||(e[8]=a=>A(!0))},{default:l(()=>[t(_,null,{default:l(()=>[t(f(Be))]),_:1}),e[19]||(e[19]=i(" 批量推荐 "))]),_:1,__:[19]}),t(h,{size:"small",onClick:e[9]||(e[9]=a=>A(!1))},{default:l(()=>[t(_,null,{default:l(()=>[t(f(Me))]),_:1}),e[20]||(e[20]=i(" 取消推荐 "))]),_:1,__:[20]}),t(h,{type:"danger",size:"small",onClick:te},{default:l(()=>[t(_,null,{default:l(()=>[t(f(q))]),_:1}),e[21]||(e[21]=i(" 批量删除 "))]),_:1,__:[21]})])])):V("",!0),$e((d(),z(he,{data:Y.value,onSelectionChange:ee,"row-key":"id",class:"recipe-table"},{default:l(()=>[t(k,{type:"selection",width:"55"}),t(k,{label:"食谱信息","min-width":"320"},{default:l(({row:a})=>{var C,M;return[s("div",Pe,[s("div",Oe,[s("img",{src:a.coverImage||"/default-recipe.jpg",alt:"封面"},null,8,Ge),s("div",He,[a.isRecommended?(d(),z(B,{key:0,type:"warning",size:"small"},{default:l(()=>e[22]||(e[22]=[i("推荐")])),_:1,__:[22]})):V("",!0),a.isFeatured?(d(),z(B,{key:1,type:"success",size:"small"},{default:l(()=>e[23]||(e[23]=[i("精选")])),_:1,__:[23]})):V("",!0)])]),s("div",Je,[s("div",Qe,o(a.title),1),s("div",qe,[s("span",We,o(a.categoryName),1),s("span",Xe,o(K(a.difficulty)),1),s("span",Ze,o(a.cookingTime)+"分钟",1)]),s("div",et,o(a.description||"暂无描述"),1),s("div",tt,[(d(!0),y(D,null,T((C=a.tags)==null?void 0:C.slice(0,3),H=>(d(),z(B,{key:H,size:"small",class:"tag-item"},{default:l(()=>[i(o(H),1)]),_:2},1024))),128)),((M=a.tags)==null?void 0:M.length)>3?(d(),y("span",at," +"+o(a.tags.length-3),1)):V("",!0)])])])]}),_:1}),t(k,{label:"作者信息",width:"150"},{default:l(({row:a})=>[s("div",lt,[t(ve,{size:32,src:a.author.avatar},{default:l(()=>[t(_,null,{default:l(()=>[t(f(Te))]),_:1})]),_:2},1032,["src"]),s("div",st,[s("div",nt,o(a.author.nickname||a.author.username),1),s("div",ot,"@"+o(a.author.username),1)])])]),_:1}),t(k,{label:"状态",width:"100"},{default:l(({row:a})=>[t(B,{type:pe(a.status),size:"small"},{default:l(()=>[i(o(me(a.status)),1)]),_:2},1032,["type"])]),_:1}),t(k,{label:"统计数据",width:"120"},{default:l(({row:a})=>[s("div",it,[s("div",null,"浏览: "+o(a.viewCount||0),1),s("div",null,"点赞: "+o(a.likeCount||0),1),s("div",null,"收藏: "+o(a.favoriteCount||0),1),s("div",null,"评论: "+o(a.commentCount||0),1)])]),_:1}),t(k,{label:"食谱数据",width:"120"},{default:l(({row:a})=>[s("div",dt,[s("div",null,"食材: "+o(a.ingredientCount||0)+"种",1),s("div",null,"步骤: "+o(a.stepCount||0)+"步",1),s("div",null,"份量: "+o(a.servings||0)+"人份",1)])]),_:1}),t(k,{label:"创建时间",width:"160"},{default:l(({row:a})=>[s("div",ut,[s("div",null,o(P(a.createdAt)),1),s("div",rt," 更新: "+o(P(a.updatedAt)),1)])]),_:1}),t(k,{label:"操作",width:"220",fixed:"right"},{default:l(({row:a})=>[s("div",ct,[t(h,{type:"primary",size:"small",onClick:C=>ue(a.id)},{default:l(()=>[t(_,null,{default:l(()=>[t(f(Ie))]),_:1}),e[24]||(e[24]=i(" 预览 "))]),_:2,__:[24]},1032,["onClick"]),t(ye,{onCommand:ae},{dropdown:l(()=>[t(ge,null,{default:l(()=>[t(R,{command:{action:"edit",recipe:a}},{default:l(()=>[t(_,null,{default:l(()=>[t(f(Ne))]),_:1}),e[26]||(e[26]=i(" 编辑食谱 "))]),_:2,__:[26]},1032,["command"]),t(R,{command:{action:"toggleStatus",recipe:a},disabled:a.status===2},{default:l(()=>[i(o(a.status===1?"下架食谱":"发布食谱"),1)]),_:2},1032,["command","disabled"]),t(R,{command:{action:"toggleRecommend",recipe:a},divided:""},{default:l(()=>[i(o(a.isRecommended?"取消推荐":"推荐食谱"),1)]),_:2},1032,["command"]),t(R,{command:{action:"feature",recipe:a}},{default:l(()=>[i(o(a.isFeatured?"取消精选":"设为精选"),1)]),_:2},1032,["command"]),t(R,{command:{action:"duplicate",recipe:a}},{default:l(()=>[t(_,null,{default:l(()=>[t(f(Ye))]),_:1}),e[27]||(e[27]=i(" 复制食谱 "))]),_:2,__:[27]},1032,["command"]),t(R,{command:{action:"export",recipe:a}},{default:l(()=>[t(_,null,{default:l(()=>[t(f(Q))]),_:1}),e[28]||(e[28]=i(" 导出数据 "))]),_:2,__:[28]},1032,["command"]),t(R,{command:{action:"delete",recipe:a},divided:""},{default:l(()=>[t(_,null,{default:l(()=>[t(f(q))]),_:1}),e[29]||(e[29]=i(" 删除食谱 "))]),_:2,__:[29]},1032,["command"])]),_:2},1024)]),default:l(()=>[t(h,{type:"text",size:"small"},{default:l(()=>[e[25]||(e[25]=i(" 更多")),t(_,{class:"el-icon--right"},{default:l(()=>[t(f(Ue))]),_:1})]),_:1,__:[25]})]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Ce,I.value]]),s("div",pt,[t(be,{"current-page":b.page,"onUpdate:currentPage":e[10]||(e[10]=a=>b.page=a),"page-size":b.pageSize,"onUpdate:pageSize":e[11]||(e[11]=a=>b.pageSize=a),total:b.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ce,onCurrentChange:re},null,8,["current-page","page-size","total"])])]),_:1}),t(ke,{modelValue:U.value,"onUpdate:modelValue":e[14]||(e[14]=a=>U.value=a),title:`食谱预览 - ${(G=c.value)==null?void 0:G.title}`,width:"80%",top:"5vh"},{footer:l(()=>{var a;return[s("span",xt,[t(h,{onClick:e[12]||(e[12]=C=>U.value=!1)},{default:l(()=>e[32]||(e[32]=[i("关闭")])),_:1,__:[32]}),(a=c.value)!=null&&a.id?(d(),z(h,{key:0,type:"primary",onClick:e[13]||(e[13]=C=>{var M;return E((M=c.value)==null?void 0:M.id)})},{default:l(()=>e[33]||(e[33]=[i(" 编辑食谱 ")])),_:1,__:[33]})):V("",!0)])]}),default:l(()=>[c.value?(d(),y("div",mt,[s("div",_t,[s("img",{src:c.value.coverImage,alt:"封面",class:"preview-cover"},null,8,ft),s("div",vt,[s("h3",null,o(c.value.title),1),s("p",null,o(c.value.description),1),s("div",gt,[s("span",null,"作者: "+o(c.value.author.nickname),1),s("span",null,"分类: "+o(c.value.categoryName),1),s("span",null,"难度: "+o(K(c.value.difficulty)),1),s("span",null,"时间: "+o(c.value.cookingTime)+"分钟",1),s("span",null,"份量: "+o(c.value.servings)+"人份",1)]),s("div",yt,[(d(!0),y(D,null,T(c.value.tags,a=>(d(),z(B,{key:a,size:"small"},{default:l(()=>[i(o(a),1)]),_:2},1024))),128))])])]),s("div",ht,[s("div",bt,[e[30]||(e[30]=s("h4",null,"所需食材",-1)),s("div",kt,[(d(!0),y(D,null,T(c.value.ingredients,a=>(d(),y("div",{key:a.name,class:"ingredient-item"},[s("span",Ct,o(a.name),1),s("span",wt,o(a.amount)+" "+o(a.unit),1)]))),128))])]),s("div",Rt,[e[31]||(e[31]=s("h4",null,"制作步骤",-1)),s("div",Vt,[(d(!0),y(D,null,T(c.value.steps,(a,C)=>(d(),y("div",{key:C,class:"step-item"},[s("div",zt,o(C+1),1),s("div",St,[s("p",null,o(a.description),1),a.image?(d(),y("img",{key:0,src:a.image,alt:"步骤图片",class:"step-image"},null,8,$t)):V("",!0)])]))),128))])])])])):V("",!0)]),_:1},8,["modelValue","title"])])}}}),It=Le(Bt,[["__scopeId","data-v-788cf458"]]);export{It as default};
