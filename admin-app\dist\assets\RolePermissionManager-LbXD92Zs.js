import{d as Q,r as m,a as W,o as X,c as b,e as o,f as t,w as l,j as i,S as ee,T as te,n as v,h as C,F as le,t as _,Y as se,P as U,J as E,Z as oe,p as w,k as f,_ as ae,$ as N,U as ne,N as ie,C as de}from"./index-BDkHONMN.js";import{_ as re}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ue={class:"role-permission-container"},ce={class:"card-header"},pe={class:"role-list"},me=["onClick"],_e={class:"role-info"},fe={class:"role-name"},ve={class:"role-description"},ye={class:"role-meta"},be={class:"user-count"},ge={class:"role-actions"},ke={class:"card-header"},he={key:0},Ce={key:0,class:"empty-state"},we={key:1,class:"permission-config"},Ve={class:"role-selected-info"},xe={class:"permission-node"},Re={class:"dialog-footer"},De=Q({__name:"RolePermissionManager",setup(Me){const c=m([{id:1,name:"超级管理员",code:"SUPER_ADMIN",description:"拥有系统所有权限",status:1,userCount:1},{id:2,name:"管理员",code:"ADMIN",description:"拥有大部分管理权限",status:1,userCount:3},{id:3,name:"版主",code:"MODERATOR",description:"内容审核和用户管理权限",status:1,userCount:5}]),d=m(null),g=m([]),F=m([{id:"dashboard",label:"控制台",icon:"DataLine",type:"页面",children:[{id:"dashboard.view",label:"查看控制台",type:"查看"}]},{id:"user",label:"用户管理",icon:"User",type:"模块",children:[{id:"user.list",label:"用户列表",type:"查看"},{id:"user.create",label:"创建用户",type:"创建"},{id:"user.edit",label:"编辑用户",type:"编辑"},{id:"user.delete",label:"删除用户",type:"删除"},{id:"user.role",label:"角色管理",type:"管理"}]},{id:"content",label:"内容管理",icon:"Document",type:"模块",children:[{id:"content.recipe",label:"食谱管理",type:"管理"},{id:"content.audit",label:"内容审核",type:"审核"},{id:"content.category",label:"分类管理",type:"管理"},{id:"content.sensitive",label:"敏感词管理",type:"管理"}]},{id:"system",label:"系统管理",icon:"Setting",type:"模块",children:[{id:"system.config",label:"系统配置",type:"配置"},{id:"system.log",label:"日志管理",type:"查看"},{id:"system.backup",label:"备份管理",type:"管理"}]}]),y=m(!1),k=m(!1),x=m(),R=m(),n=W({id:null,name:"",code:"",description:"",status:1}),$={name:[{required:!0,message:"请输入角色名称",trigger:"blur"}],code:[{required:!0,message:"请输入角色标识",trigger:"blur"}]},D=a=>{d.value=a,g.value=["dashboard.view","user.list","content.recipe"]},B=(a,{checkedKeys:e})=>{g.value=e},I=a=>({页面:"primary",模块:"success",查看:"info",创建:"warning",编辑:"primary",删除:"danger",管理:"success",审核:"warning",配置:"primary"})[a]||"info",O=()=>{k.value=!1,S(),y.value=!0},j=a=>{k.value=!0,Object.assign(n,a),y.value=!0},A=async a=>{var e;try{await de.confirm(`确定要删除角色"${a.name}"吗？`,"提示",{type:"warning"});const r=c.value.findIndex(u=>u.id===a.id);r>-1&&(c.value.splice(r,1),w.success("删除成功"),((e=d.value)==null?void 0:e.id)===a.id&&(d.value=null))}catch{}},S=()=>{Object.assign(n,{id:null,name:"",code:"",description:"",status:1})},q=async()=>{var a;try{if(await((a=x.value)==null?void 0:a.validate()),k.value){const e=c.value.findIndex(r=>r.id===n.id);e>-1&&Object.assign(c.value[e],n),w.success("角色更新成功")}else c.value.push({...n,id:Date.now(),userCount:0}),w.success("角色创建成功");y.value=!1}catch{}},L=()=>{d.value&&w.success("权限配置保存成功")},J=()=>{var a;(a=R.value)==null||a.setCheckedKeys([]),g.value=[]};return X(()=>{c.value.length>0&&D(c.value[0])}),(a,e)=>{const r=i("el-icon"),u=i("el-button"),M=i("el-tag"),P=i("el-card"),z=i("el-col"),K=i("el-empty"),Y=i("el-row"),V=i("el-input"),h=i("el-form-item"),Z=i("el-switch"),G=i("el-form"),H=i("el-dialog");return f(),b("div",ue,[e[13]||(e[13]=o("div",{class:"page-header"},[o("h1",null,"角色权限管理"),o("p",null,"管理系统角色和权限配置")],-1)),t(Y,{gutter:20},{default:l(()=>[t(z,{span:8},{default:l(()=>[t(P,null,{header:l(()=>[o("div",ce,[e[7]||(e[7]=o("span",null,"角色列表",-1)),t(u,{type:"primary",size:"small",onClick:O},{default:l(()=>[t(r,null,{default:l(()=>[t(C(le))]),_:1}),e[6]||(e[6]=v(" 添加角色 "))]),_:1,__:[6]})])]),default:l(()=>[o("div",pe,[(f(!0),b(ee,null,te(c.value,s=>{var p;return f(),b("div",{key:s.id,class:ae(["role-item",{active:((p=d.value)==null?void 0:p.id)===s.id}]),onClick:T=>D(s)},[o("div",_e,[o("div",fe,_(s.name),1),o("div",ve,_(s.description),1),o("div",ye,[t(M,{type:s.status===1?"success":"danger",size:"small"},{default:l(()=>[v(_(s.status===1?"启用":"禁用"),1)]),_:2},1032,["type"]),o("span",be,_(s.userCount)+" 人",1)])]),o("div",ge,[t(u,{type:"text",size:"small",onClick:N(T=>j(s),["stop"])},{default:l(()=>[t(r,null,{default:l(()=>[t(C(ne))]),_:1})]),_:2},1032,["onClick"]),t(u,{type:"text",size:"small",onClick:N(T=>A(s),["stop"])},{default:l(()=>[t(r,null,{default:l(()=>[t(C(ie))]),_:1})]),_:2},1032,["onClick"])])],10,me)}),128))])]),_:1})]),_:1}),t(z,{span:16},{default:l(()=>[t(P,null,{header:l(()=>[o("div",ke,[e[10]||(e[10]=o("span",null,"权限配置",-1)),d.value?(f(),b("div",he,[t(u,{onClick:L,type:"primary",size:"small"},{default:l(()=>e[8]||(e[8]=[v(" 保存配置 ")])),_:1,__:[8]}),t(u,{onClick:J,size:"small"},{default:l(()=>e[9]||(e[9]=[v(" 重置 ")])),_:1,__:[9]})])):E("",!0)])]),default:l(()=>[d.value?(f(),b("div",we,[o("div",Ve,[o("h3",null,_(d.value.name),1),o("p",null,_(d.value.description),1)]),t(C(se),{ref_key:"permissionTreeRef",ref:R,data:F.value,"show-checkbox":"","node-key":"id","default-expand-all":!0,"default-checked-keys":g.value,onCheck:B},{default:l(({node:s,data:p})=>[o("span",xe,[t(r,null,{default:l(()=>[(f(),U(oe(p.icon)))]),_:2},1024),o("span",null,_(p.label),1),p.type?(f(),U(M,{key:0,type:I(p.type),size:"small"},{default:l(()=>[v(_(p.type),1)]),_:2},1032,["type"])):E("",!0)])]),_:1},8,["data","default-checked-keys"])])):(f(),b("div",Ce,[t(K,{description:"请选择一个角色来配置权限"})]))]),_:1})]),_:1})]),_:1}),t(H,{modelValue:y.value,"onUpdate:modelValue":e[5]||(e[5]=s=>y.value=s),title:k.value?"编辑角色":"添加角色",width:"600px"},{footer:l(()=>[o("div",Re,[t(u,{onClick:e[4]||(e[4]=s=>y.value=!1)},{default:l(()=>e[11]||(e[11]=[v("取消")])),_:1,__:[11]}),t(u,{type:"primary",onClick:q},{default:l(()=>e[12]||(e[12]=[v("确定")])),_:1,__:[12]})])]),default:l(()=>[t(G,{ref_key:"roleFormRef",ref:x,model:n,rules:$,"label-width":"80px"},{default:l(()=>[t(h,{label:"角色名称",prop:"name"},{default:l(()=>[t(V,{modelValue:n.name,"onUpdate:modelValue":e[0]||(e[0]=s=>n.name=s),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),t(h,{label:"角色标识",prop:"code"},{default:l(()=>[t(V,{modelValue:n.code,"onUpdate:modelValue":e[1]||(e[1]=s=>n.code=s),placeholder:"请输入角色标识"},null,8,["modelValue"])]),_:1}),t(h,{label:"角色描述",prop:"description"},{default:l(()=>[t(V,{modelValue:n.description,"onUpdate:modelValue":e[2]||(e[2]=s=>n.description=s),type:"textarea",placeholder:"请输入角色描述",rows:3},null,8,["modelValue"])]),_:1}),t(h,{label:"状态"},{default:l(()=>[t(Z,{modelValue:n.status,"onUpdate:modelValue":e[3]||(e[3]=s=>n.status=s),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),Te=re(De,[["__scopeId","data-v-a2612ba3"]]);export{Te as default};
