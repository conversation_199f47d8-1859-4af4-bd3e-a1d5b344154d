import{d as Q,r as i,l as W,z as ee,D as te,B as ae,o as se,ag as le,ah as ne,p as oe,c as d,e,f as l,j as u,w as a,n as y,h as T,I as re,S as h,T as k,k as r,P as b,_ as m,Z as ie,t as o,ai as de,s as ue}from"./index-BDkHONMN.js";import{i as Y}from"./index-BNz9ATsT.js";import{_ as ce}from"./_plugin-vue_export-helper-DlAUqK2U.js";const _e={class:"stats-overview-page"},ve={class:"page-header"},fe={class:"header-actions"},pe={class:"metric-content"},me={class:"metric-icon"},ge={class:"metric-info"},ye={class:"metric-value"},he={class:"metric-label"},ke={class:"metric-trend"},be={class:"chart-header"},we={class:"today-stats"},Ce={class:"stat-label"},xe={class:"stat-value"},De={class:"ranking-list"},Se={class:"ranking-info"},Me={class:"ranking-title"},Re={class:"ranking-data"},Ve={class:"system-status"},Oe={class:"status-info"},Te={class:"status-label"},Ye={class:"status-value"},Ue=Q({__name:"Overview",setup(Be){const w=i(!1),C=i([]),U=i("7days"),x=i(),D=i(),S=i();let v=null,f=null,p=null;const I=i([{key:"users",label:"总用户数",value:12580,trend:8.5,icon:W,color:"#409eff"},{key:"recipes",label:"总食谱数",value:3426,trend:12.3,icon:ee,color:"#67c23a"},{key:"views",label:"总浏览量",value:856420,trend:15.7,icon:te,color:"#e6a23c"},{key:"likes",label:"总点赞数",value:45632,trend:-2.1,icon:ae,color:"#f56c6c"}]),N=i([{key:"newUsers",label:"新增用户",value:128,change:15},{key:"newRecipes",label:"新增食谱",value:45,change:8},{key:"activeUsers",label:"活跃用户",value:1250,change:-5},{key:"comments",label:"新增评论",value:89,change:23}]),F=i([{id:1,title:"红烧肉的经典做法",views:2580},{id:2,title:"家常小炒菜谱大全",views:1890},{id:3,title:"营养早餐搭配指南",views:1456},{id:4,title:"简单易学的蛋糕制作",views:1230},{id:5,title:"川菜麻婆豆腐正宗做法",views:1089}]),G=i([{key:"server",label:"服务器",value:"正常运行",status:"normal"},{key:"database",label:"数据库",value:"连接正常",status:"normal"},{key:"storage",label:"存储空间",value:"68% 使用",status:"warning"},{key:"memory",label:"内存使用",value:"72% 使用",status:"normal"}]);se(()=>{L(),M(),le(()=>{$()})}),ne(()=>{v==null||v.dispose(),f==null||f.dispose(),p==null||p.dispose()});const L=()=>{const n=new Date,t=new Date;t.setDate(t.getDate()-7),C.value=[t.toISOString().split("T")[0],n.toISOString().split("T")[0]]},M=async()=>{w.value=!0;try{await new Promise(n=>setTimeout(n,1e3))}catch{oe.error("加载数据失败")}finally{w.value=!1}},P=()=>{M(),R(),B(),z()},E=()=>{M()},$=()=>{j(),K(),X()},j=()=>{x.value&&(v=Y(x.value),R())},R=()=>{if(!v)return;const n={tooltip:{trigger:"axis"},legend:{data:["用户数","食谱数","浏览量"]},grid:{left:10,right:10,top:50,bottom:20,containLabel:!0},xAxis:{type:"category",data:["01-14","01-15","01-16","01-17","01-18","01-19","01-20"]},yAxis:{type:"value"},series:[{name:"用户数",type:"line",smooth:!0,data:[120,150,180,220,200,250,280],lineStyle:{color:"#409eff"}},{name:"食谱数",type:"line",smooth:!0,data:[45,52,38,65,48,72,58],lineStyle:{color:"#67c23a"}},{name:"浏览量",type:"line",smooth:!0,data:[1200,1350,1180,1650,1480,1720,1580],lineStyle:{color:"#e6a23c"}}]};v.setOption(n)},K=()=>{D.value&&(f=Y(D.value),B())},B=()=>{if(!f)return;const n={tooltip:{trigger:"axis"},grid:{left:10,right:10,top:20,bottom:20,containLabel:!0},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{name:"新增用户",type:"bar",data:[85,92,78,105,88,120,95],itemStyle:{color:"#409eff"}}]};f.setOption(n)},X=()=>{S.value&&(p=Y(S.value),z())},z=()=>{if(!p)return;const n={tooltip:{trigger:"item"},series:[{type:"pie",radius:"70%",data:[{value:1048,name:"家常菜"},{value:735,name:"甜品"},{value:580,name:"汤品"},{value:484,name:"素食"},{value:300,name:"其他"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};p.setOption(n)},Z=n=>n>=1e6?(n/1e6).toFixed(1)+"M":n>=1e3?(n/1e3).toFixed(1)+"K":n.toString();return(n,t)=>{const q=u("el-date-picker"),V=u("el-icon"),H=u("el-button"),c=u("el-card"),_=u("el-col"),g=u("el-row"),O=u("el-radio-button"),J=u("el-radio-group");return r(),d("div",_e,[e("div",ve,[t[3]||(t[3]=e("h2",null,"数据概览",-1)),t[4]||(t[4]=e("p",null,"系统整体运营数据概览",-1)),e("div",fe,[l(q,{modelValue:C.value,"onUpdate:modelValue":t[0]||(t[0]=s=>C.value=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:E},null,8,["modelValue"]),l(H,{onClick:P,loading:w.value},{default:a(()=>[l(V,null,{default:a(()=>[l(T(re))]),_:1}),t[2]||(t[2]=y(" 刷新数据 "))]),_:1,__:[2]},8,["loading"])])]),l(g,{gutter:20,class:"metrics-section"},{default:a(()=>[(r(!0),d(h,null,k(I.value,s=>(r(),b(_,{xs:24,sm:12,lg:6,key:s.key},{default:a(()=>[l(c,{class:m(["metric-card",`metric-${s.key}`]),shadow:"hover"},{default:a(()=>[e("div",pe,[e("div",me,[l(V,{size:32,color:s.color},{default:a(()=>[(r(),b(ie(s.icon)))]),_:2},1032,["color"])]),e("div",ge,[e("div",ye,o(Z(s.value)),1),e("div",he,o(s.label),1),e("div",ke,[l(V,{class:m(s.trend>0?"trend-up":"trend-down")},{default:a(()=>[s.trend>0?(r(),b(T(de),{key:0})):(r(),b(T(ue),{key:1}))]),_:2},1032,["class"]),e("span",{class:m(s.trend>0?"trend-up":"trend-down")},o(s.trend>0?"+":"")+o(Math.abs(s.trend))+"% ",3),t[5]||(t[5]=e("span",{class:"trend-label"},"vs 上周",-1))])])])]),_:2},1032,["class"])]),_:2},1024))),128))]),_:1}),l(g,{gutter:20,class:"charts-section"},{default:a(()=>[l(_,{span:24},{default:a(()=>[l(c,{class:"chart-card"},{header:a(()=>[e("div",be,[t[9]||(t[9]=e("span",null,"整体数据趋势",-1)),l(J,{modelValue:U.value,"onUpdate:modelValue":t[1]||(t[1]=s=>U.value=s),size:"small",onChange:R},{default:a(()=>[l(O,{label:"7days"},{default:a(()=>t[6]||(t[6]=[y("近7天")])),_:1,__:[6]}),l(O,{label:"30days"},{default:a(()=>t[7]||(t[7]=[y("近30天")])),_:1,__:[7]}),l(O,{label:"90days"},{default:a(()=>t[8]||(t[8]=[y("近90天")])),_:1,__:[8]})]),_:1},8,["modelValue"])])]),default:a(()=>[e("div",{class:"chart-container",ref_key:"trendChartRef",ref:x},null,512)]),_:1})]),_:1})]),_:1}),l(g,{gutter:20,class:"charts-section"},{default:a(()=>[l(_,{xs:24,lg:12},{default:a(()=>[l(c,{class:"chart-card"},{header:a(()=>t[10]||(t[10]=[e("span",null,"用户增长分析",-1)])),default:a(()=>[e("div",{class:"chart-container",ref_key:"userGrowthChartRef",ref:D},null,512)]),_:1})]),_:1}),l(_,{xs:24,lg:12},{default:a(()=>[l(c,{class:"chart-card"},{header:a(()=>t[11]||(t[11]=[e("span",null,"内容发布分析",-1)])),default:a(()=>[e("div",{class:"chart-container",ref_key:"contentChartRef",ref:S},null,512)]),_:1})]),_:1})]),_:1}),l(g,{gutter:20,class:"table-section"},{default:a(()=>[l(_,{xs:24,lg:8},{default:a(()=>[l(c,{class:"data-card"},{header:a(()=>t[12]||(t[12]=[e("span",null,"今日数据",-1)])),default:a(()=>[e("div",we,[(r(!0),d(h,null,k(N.value,s=>(r(),d("div",{key:s.key,class:"stat-item"},[e("div",Ce,o(s.label),1),e("div",xe,o(s.value),1),e("div",{class:m(["stat-change",s.change>0?"increase":"decrease"])},o(s.change>0?"+":"")+o(s.change),3)]))),128))])]),_:1})]),_:1}),l(_,{xs:24,lg:8},{default:a(()=>[l(c,{class:"data-card"},{header:a(()=>t[13]||(t[13]=[e("span",null,"热门内容排行",-1)])),default:a(()=>[e("div",De,[(r(!0),d(h,null,k(F.value,(s,A)=>(r(),d("div",{key:s.id,class:"ranking-item"},[e("div",{class:m(["ranking-number",`rank-${A+1}`])},o(A+1),3),e("div",Se,[e("div",Me,o(s.title),1),e("div",Re,o(s.views)+" 浏览",1)])]))),128))])]),_:1})]),_:1}),l(_,{xs:24,lg:8},{default:a(()=>[l(c,{class:"data-card"},{header:a(()=>t[14]||(t[14]=[e("span",null,"系统状态",-1)])),default:a(()=>[e("div",Ve,[(r(!0),d(h,null,k(G.value,s=>(r(),d("div",{key:s.key,class:"status-item"},[e("div",{class:m(["status-indicator",s.status])},null,2),e("div",Oe,[e("div",Te,o(s.label),1),e("div",Ye,o(s.value),1)])]))),128))])]),_:1})]),_:1})]),_:1})])}}}),Ne=ce(Ue,[["__scopeId","data-v-65ebf688"]]);export{Ne as default};
