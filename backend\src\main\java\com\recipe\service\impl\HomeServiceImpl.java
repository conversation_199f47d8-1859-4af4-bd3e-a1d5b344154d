package com.recipe.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.recipe.dto.HomeStatsDTO;
import com.recipe.dto.LatestRecipeDTO;
import com.recipe.dto.PopularCreatorDTO;
import com.recipe.entity.Recipe;
import com.recipe.entity.User;
import com.recipe.mapper.RecipeMapper;
import com.recipe.mapper.UserMapper;
import com.recipe.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 首页服务实现类
 */
@Slf4j
@Service
public class HomeServiceImpl implements IHomeService {

    @Autowired
    private IUserService userService;

    @Autowired
    private IRecipeService recipeService;

    @Autowired
    private IRecipeFavoriteService favoriteService;

    @Autowired
    private IFollowService followService;

    @Autowired
    private IRecipeLikeService likeService;

    @Autowired
    private RecipeMapper recipeMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public HomeStatsDTO getHomeStats(Long userId) {
        HomeStatsDTO homeStats = new HomeStatsDTO();

        // 获取全站统计数据
        HomeStatsDTO.GlobalStats globalStats = new HomeStatsDTO.GlobalStats();
        
        // 总用户数（排除已删除）
        globalStats.setTotalUsers(userService.count(
            new QueryWrapper<User>().eq("deleted", 0)
        ));

        // 总食谱数（已发布状态）
        globalStats.setTotalRecipes(recipeService.count(
            new QueryWrapper<Recipe>().eq("status", 1)
        ));

        // 总浏览量
        globalStats.setTotalViews(recipeMapper.getTotalViews());

        // 总点赞数
        globalStats.setTotalLikes(likeService.count());

        // 总收藏数
        globalStats.setTotalFavorites(favoriteService.count());

        // 今日新增用户数
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        globalStats.setTodayNewUsers(userService.count(
            new QueryWrapper<User>()
                .eq("deleted", 0)
                .ge("created_at", today)
        ));

        // 今日新增食谱数
        globalStats.setTodayNewRecipes(recipeService.count(
            new QueryWrapper<Recipe>()
                .eq("status", 1)
                .ge("created_at", today)
        ));

        // 活跃用户数（近7天有登录的用户）
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        globalStats.setActiveUsers(userService.count(
            new QueryWrapper<User>()
                .eq("deleted", 0)
                .ge("last_login_time", sevenDaysAgo)
        ));

        homeStats.setGlobalStats(globalStats);

        // 如果用户已登录，获取个人统计数据
        if (userId != null) {
            HomeStatsDTO.UserStats userStats = new HomeStatsDTO.UserStats();
            
            // 我的食谱数
            userStats.setMyRecipeCount(recipeService.count(
                new QueryWrapper<Recipe>().eq("user_id", userId)
            ));

            // 我的收藏数
            userStats.setMyFavoriteCount(favoriteService.count(
                new QueryWrapper<>().eq("user_id", userId)
            ));

            // 我的关注数
            userStats.setMyFollowingCount(followService.count(
                new QueryWrapper<>().eq("follower_id", userId)
            ));

            // 我的粉丝数
            userStats.setMyFollowersCount(followService.count(
                new QueryWrapper<>().eq("following_id", userId)
            ));

            // 我的食谱总浏览量
            userStats.setMyTotalViews(recipeMapper.getTotalViewsByUserId(userId));

            // 我的食谱总点赞数
            userStats.setMyTotalLikes(recipeMapper.getTotalLikesByUserId(userId));

            homeStats.setUserStats(userStats);
        }

        return homeStats;
    }

    @Override
    public List<LatestRecipeDTO> getLatestRecipes(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 8;
        }
        return recipeMapper.selectLatestRecipes(limit);
    }

    @Override
    public List<PopularCreatorDTO> getPopularCreators(Long userId, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 6;
        }
        return userMapper.selectPopularCreators(userId, limit);
    }

    @Override
    public List<LatestRecipeDTO> getRecommendedRecipes(Long userId, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 8;
        }
        
        // 如果用户已登录，基于用户的收藏和关注进行推荐
        if (userId != null) {
            return recipeMapper.selectRecommendedRecipes(userId, limit);
        } else {
            // 未登录用户返回热门食谱
            return recipeMapper.selectPopularRecipesForHome(limit);
        }
    }

    @Override
    public List<String> getHotSearchKeywords(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        
        // 这里可以基于搜索日志或热门标签来实现
        // 暂时返回一些常见的搜索关键词
        List<String> keywords = Arrays.asList(
            "红烧肉", "蛋炒饭", "糖醋里脊", "宫保鸡丁", "麻婆豆腐",
            "西红柿鸡蛋", "青椒肉丝", "回锅肉", "鱼香肉丝", "水煮鱼",
            "提拉米苏", "芝士蛋糕", "布丁", "马卡龙", "司康饼"
        );
        
        return keywords.subList(0, Math.min(limit, keywords.size()));
    }
}
