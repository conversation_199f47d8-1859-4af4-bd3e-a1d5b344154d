<template>
  <div class="recipe-list-view">
    <div class="page-container">
      <!-- 顶部搜索区域 -->
      <div class="search-section card">
        <div class="search-header">
          <h1 class="page-title">美食食谱</h1>
          <p class="page-subtitle">发现美味，分享快乐</p>
          <div class="header-actions">
            <el-button type="primary" @click="$router.push('/categories')">
              <el-icon><Grid /></el-icon>
              浏览分类
            </el-button>
          </div>
        </div>
        
        <div class="search-form">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索食谱名称、食材或标签..."
            size="large"
            clearable
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button type="primary" @click="handleSearch">
                搜索
              </el-button>
            </template>
          </el-input>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-item">
            <label>分类：</label>
            <el-select v-model="searchForm.categoryId" placeholder="选择分类" clearable>
              <el-option label="全部分类" :value="null" />
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </div>

          <div class="filter-item">
            <label>难度：</label>
            <el-select v-model="searchForm.difficulty" placeholder="选择难度" clearable>
              <el-option label="全部难度" :value="null" />
              <el-option label="简单" :value="1" />
              <el-option label="中等" :value="2" />
              <el-option label="困难" :value="3" />
            </el-select>
          </div>

          <div class="filter-item">
            <label>排序：</label>
            <el-select v-model="searchForm.sortBy" placeholder="排序方式">
              <el-option label="最新发布" value="created_at" />
              <el-option label="最多浏览" value="views" />
              <el-option label="最多点赞" value="likes" />
              <el-option label="最多收藏" value="favorites" />
            </el-select>
          </div>

          <el-button type="primary" @click="handleSearch">
            <el-icon><Filter /></el-icon>
            筛选
          </el-button>
        </div>
      </div>

      <!-- 食谱列表 -->
      <div class="recipe-list">
        <el-row :gutter="20">
          <el-col
            v-for="recipe in recipes"
            :key="recipe.id"
            :xs="24"
            :sm="12"
            :md="8"
            :lg="6"
            class="recipe-item-col"
          >
            <div class="recipe-card card" @click="goToRecipeDetail(recipe.id)">
              <div class="recipe-image">
                <el-image
                  :src="getFileUrl(recipe.coverImage) || '/default-recipe.jpg'"
                  :alt="recipe.title"
                  fit="cover"
                  class="cover-image"
                />
                <div class="recipe-overlay">
                  <div class="recipe-stats">
                    <span class="stat-item">
                      <el-icon><View /></el-icon>
                      {{ recipe.views || 0 }}
                    </span>
                    <span class="stat-item">
                      <el-icon><Star /></el-icon>
                      {{ recipe.likes || 0 }}
                    </span>
                  </div>
                  <div class="difficulty-badge" :class="`difficulty-${recipe.difficulty}`">
                    {{ getDifficultyText(recipe.difficulty) }}
                  </div>
                </div>
              </div>

              <div class="recipe-content">
                <h3 class="recipe-title">{{ recipe.title }}</h3>
                <p class="recipe-description">{{ recipe.description }}</p>
                
                <div class="recipe-meta">
                  <div class="meta-item">
                    <el-icon><Timer /></el-icon>
                    <span>{{ getTotalTime(recipe.prepTime, recipe.cookTime) }}分钟</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><User /></el-icon>
                    <span>{{ recipe.servings }}人份</span>
                  </div>
                </div>

                <div class="recipe-tags" v-if="recipe.tags">
                  <el-tag
                    v-for="tag in getTagList(recipe.tags)"
                    :key="tag"
                    size="small"
                    class="recipe-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>

                <div class="recipe-footer">
                  <div class="author-info">
                    <el-avatar :size="24" :src="getFileUrl(recipe.creatorAvatar)">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                    <span class="author-name">{{ recipe.creatorName }}</span>
                  </div>
                  <span class="created-time">
                    {{ formatDate(recipe.createdAt) }}
                  </span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 加载更多 -->
        <div class="load-more" v-if="hasMore">
          <el-button
            type="primary"
            :loading="loading"
            @click="loadMore"
            size="large"
          >
            {{ loading ? '加载中...' : '加载更多' }}
          </el-button>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" v-if="!loading && recipes.length === 0">
          <el-empty description="暂无食谱数据">
            <el-button type="primary" @click="$router.push('/recipe/create')">
              创建第一个食谱
            </el-button>
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Search,
  Filter,
  View,
  Star,
  Timer,
  User,
  Grid
} from '@element-plus/icons-vue'
import type { Recipe, RecipeSearchForm } from '@/types/recipe'
import type { Category } from '@/types/category'
import { categoriesApi } from '@/api/categories'
import { recipesApi } from '@/api/recipes'
import { getFileUrl } from '@/utils/file'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const recipes = ref<Recipe[]>([])
const categories = ref<Category[]>([])
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = ref(12)

// 搜索表单
const searchForm = reactive<RecipeSearchForm>({
  keyword: '',
  categoryId: null,
  difficulty: null,
  sortBy: 'created_at'
})

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await categoriesApi.getActiveCategories()
    categories.value = response.data || []
  } catch (error) {
    console.error('获取分类失败:', error)
    // 如果API请求失败，使用备用数据
    categories.value = [
      { id: 1, name: '中式料理', description: '传统中式菜肴', icon: 'icon-chinese', sortOrder: 1, status: 1, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
      { id: 2, name: '西式料理', description: '西方菜肴', icon: 'icon-western', sortOrder: 2, status: 1, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
      { id: 3, name: '日式料理', description: '日本菜肴', icon: 'icon-japanese', sortOrder: 3, status: 1, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
      { id: 4, name: '韩式料理', description: '韩国菜肴', icon: 'icon-korean', sortOrder: 4, status: 1, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
      { id: 5, name: '甜品烘焙', description: '甜点和烘焙', icon: 'icon-dessert', sortOrder: 5, status: 1, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
      { id: 6, name: '汤品饮品', description: '汤类和饮品', icon: 'icon-soup', sortOrder: 6, status: 1, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
    ]
  }
}

// 获取食谱列表
const fetchRecipes = async (loadMore = false) => {
  if (loading.value) return

  loading.value = true
  try {
    if (!loadMore) {
      currentPage.value = 1
      recipes.value = []
    }

    // 调用API获取食谱列表
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      keyword: searchForm.keyword,
      categoryId: searchForm.categoryId,
      difficulty: searchForm.difficulty,
      sortBy: searchForm.sortBy
    }

    const { data } = await recipesApi.getRecipes(params)
    
    if (data && data.content) {
      recipes.value = loadMore ? [...recipes.value, ...data.content] : data.content
      hasMore.value = !data.last
      currentPage.value++
    }

  } catch (error) {
    console.error('获取食谱列表失败:', error)
    ElMessage.error('获取食谱列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  fetchRecipes(false)
}

// 加载更多
const loadMore = () => {
  fetchRecipes(true)
}

// 跳转到食谱详情
const goToRecipeDetail = (id: number) => {
  router.push(`/recipe/${id}`)
}

// 获取难度文本
const getDifficultyText = (difficulty: number): string => {
  const difficultyMap: Record<number, string> = {
    1: '简单',
    2: '中等', 
    3: '困难'
  }
  return difficultyMap[difficulty] || '未知'
}

// 获取总时间
const getTotalTime = (prepTime: number, cookTime: number): number => {
  return (prepTime || 0) + (cookTime || 0)
}

// 获取标签列表
const getTagList = (tags?: string): string[] => {
  return tags ? tags.split(',').filter(tag => tag.trim()) : []
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 组件挂载
onMounted(() => {
  fetchCategories()
  fetchRecipes()
})
</script>

<style scoped>
.recipe-list-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  padding: 20px 0;
}

.search-section {
  margin-bottom: 30px;
  padding: 30px;
  text-align: center;
}

.search-header {
  margin-bottom: 30px;
  position: relative;
}

.header-actions {
  position: absolute;
  top: 0;
  right: 0;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 10px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.search-input {
  max-width: 600px;
  margin: 0 auto 20px;
}

.filter-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

/* 食谱卡片 */
.recipe-list {
  margin-top: 20px;
}

.recipe-item-col {
  margin-bottom: 20px;
}

.recipe-card {
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.recipe-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.recipe-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.recipe-card:hover .cover-image {
  transform: scale(1.05);
}

.recipe-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.3));
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 15px;
}

.recipe-stats {
  display: flex;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: white;
  font-size: 0.9rem;
}

.difficulty-badge {
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.difficulty-1 { color: #67c23a; }
.difficulty-2 { color: #e6a23c; }
.difficulty-3 { color: #f56c6c; }

.recipe-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.recipe-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recipe-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 15px;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recipe-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.recipe-tags {
  margin-bottom: 15px;
}

.recipe-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.recipe-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.created-time {
  color: var(--text-light);
  font-size: 0.8rem;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: 40px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-item {
    justify-content: space-between;
  }
  
  .page-title {
    font-size: 2rem;
  }
}
</style> 