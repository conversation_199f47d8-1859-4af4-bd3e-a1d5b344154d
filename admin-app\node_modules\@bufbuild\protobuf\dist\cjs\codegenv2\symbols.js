"use strict";
// Copyright 2021-2025 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.symbols = exports.wktPublicImportPaths = exports.packageName = void 0;
/**
 * @private
 */
exports.packageName = "@bufbuild/protobuf";
/**
 * @private
 */
exports.wktPublicImportPaths = {
    "google/protobuf/compiler/plugin.proto": exports.packageName + "/wkt",
    "google/protobuf/any.proto": exports.packageName + "/wkt",
    "google/protobuf/api.proto": exports.packageName + "/wkt",
    "google/protobuf/cpp_features.proto": exports.packageName + "/wkt",
    "google/protobuf/descriptor.proto": exports.packageName + "/wkt",
    "google/protobuf/duration.proto": exports.packageName + "/wkt",
    "google/protobuf/empty.proto": exports.packageName + "/wkt",
    "google/protobuf/field_mask.proto": exports.packageName + "/wkt",
    "google/protobuf/go_features.proto": exports.packageName + "/wkt",
    "google/protobuf/java_features.proto": exports.packageName + "/wkt",
    "google/protobuf/source_context.proto": exports.packageName + "/wkt",
    "google/protobuf/struct.proto": exports.packageName + "/wkt",
    "google/protobuf/timestamp.proto": exports.packageName + "/wkt",
    "google/protobuf/type.proto": exports.packageName + "/wkt",
    "google/protobuf/wrappers.proto": exports.packageName + "/wkt",
};
/**
 * @private
 */
// biome-ignore format: want this to read well
exports.symbols = {
    isMessage: { typeOnly: false, bootstrapWktFrom: "../../is-message.js", from: exports.packageName },
    Message: { typeOnly: true, bootstrapWktFrom: "../../types.js", from: exports.packageName },
    create: { typeOnly: false, bootstrapWktFrom: "../../create.js", from: exports.packageName },
    fromJson: { typeOnly: false, bootstrapWktFrom: "../../from-json.js", from: exports.packageName },
    fromJsonString: { typeOnly: false, bootstrapWktFrom: "../../from-json.js", from: exports.packageName },
    fromBinary: { typeOnly: false, bootstrapWktFrom: "../../from-binary.js", from: exports.packageName },
    toBinary: { typeOnly: false, bootstrapWktFrom: "../../to-binary.js", from: exports.packageName },
    toJson: { typeOnly: false, bootstrapWktFrom: "../../to-json.js", from: exports.packageName },
    toJsonString: { typeOnly: false, bootstrapWktFrom: "../../to-json.js", from: exports.packageName },
    protoInt64: { typeOnly: false, bootstrapWktFrom: "../../proto-int64.js", from: exports.packageName },
    JsonValue: { typeOnly: true, bootstrapWktFrom: "../../json-value.js", from: exports.packageName },
    JsonObject: { typeOnly: true, bootstrapWktFrom: "../../json-value.js", from: exports.packageName },
    codegen: {
        boot: { typeOnly: false, bootstrapWktFrom: "../../codegenv2/boot.js", from: exports.packageName + "/codegenv2" },
        fileDesc: { typeOnly: false, bootstrapWktFrom: "../../codegenv2/file.js", from: exports.packageName + "/codegenv2" },
        enumDesc: { typeOnly: false, bootstrapWktFrom: "../../codegenv2/enum.js", from: exports.packageName + "/codegenv2" },
        extDesc: { typeOnly: false, bootstrapWktFrom: "../../codegenv2/extension.js", from: exports.packageName + "/codegenv2" },
        messageDesc: { typeOnly: false, bootstrapWktFrom: "../../codegenv2/message.js", from: exports.packageName + "/codegenv2" },
        serviceDesc: { typeOnly: false, bootstrapWktFrom: "../../codegenv2/service.js", from: exports.packageName + "/codegenv2" },
        tsEnum: { typeOnly: false, bootstrapWktFrom: "../../codegenv2/enum.js", from: exports.packageName + "/codegenv2" },
        GenFile: { typeOnly: true, bootstrapWktFrom: "../../codegenv2/types.js", from: exports.packageName + "/codegenv2" },
        GenEnum: { typeOnly: true, bootstrapWktFrom: "../../codegenv2/types.js", from: exports.packageName + "/codegenv2" },
        GenExtension: { typeOnly: true, bootstrapWktFrom: "../../codegenv2/types.js", from: exports.packageName + "/codegenv2" },
        GenMessage: { typeOnly: true, bootstrapWktFrom: "../../codegenv2/types.js", from: exports.packageName + "/codegenv2" },
        GenService: { typeOnly: true, bootstrapWktFrom: "../../codegenv2/types.js", from: exports.packageName + "/codegenv2" },
    },
};
