package com.recipe.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 最新食谱DTO
 */
@Data
@Schema(description = "最新食谱信息")
public class LatestRecipeDTO {

    @Schema(description = "食谱ID")
    private Long id;

    @Schema(description = "食谱标题")
    private String title;

    @Schema(description = "食谱描述")
    private String description;

    @Schema(description = "封面图片")
    private String coverImage;

    @Schema(description = "准备时间（分钟）")
    private Integer prepTime;

    @Schema(description = "烹饪时间（分钟）")
    private Integer cookTime;

    @Schema(description = "难度等级：1-简单, 2-中等, 3-困难")
    private Integer difficulty;

    @Schema(description = "份量")
    private Integer servings;

    @Schema(description = "浏览量")
    private Long views;

    @Schema(description = "点赞数")
    private Long likes;

    @Schema(description = "收藏数")
    private Long favorites;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "创建者ID")
    private Long creatorId;

    @Schema(description = "创建者用户名")
    private String creatorUsername;

    @Schema(description = "创建者昵称")
    private String creatorNickname;

    @Schema(description = "创建者头像")
    private String creatorAvatar;

    @Schema(description = "标签")
    private String tags;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
