<template>
  <div class="my-recipes-view">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header card">
        <div class="header-content">
          <div class="header-info">
            <h1 class="page-title">
              <el-icon><Dish /></el-icon>
              我的食谱
            </h1>
            <p class="page-subtitle">管理您创建的所有食谱</p>
          </div>
          
          <div class="header-actions">
            <el-button type="primary" size="large" @click="$router.push('/recipe/create')">
              <el-icon><Plus /></el-icon>
              创建新食谱
            </el-button>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
          <div class="stat-card">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总食谱</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats.published }}</div>
            <div class="stat-label">已发布</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats.drafts }}</div>
            <div class="stat-label">草稿</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats.totalViews }}</div>
            <div class="stat-label">总浏览量</div>
          </div>
        </div>
      </div>

      <!-- 筛选和搜索 -->
      <div class="filter-section card">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索食谱标题..."
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          
          <el-col :span="4">
            <el-select v-model="statusFilter" placeholder="状态筛选" clearable>
              <el-option label="全部状态" :value="null" />
              <el-option label="已发布" :value="1" />
              <el-option label="草稿" :value="0" />
              <el-option label="已下架" :value="2" />
            </el-select>
          </el-col>
          
          <el-col :span="4">
            <el-select v-model="categoryFilter" placeholder="分类筛选" clearable>
              <el-option label="全部分类" :value="null" />
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </el-col>
          
          <el-col :span="4">
            <el-select v-model="sortBy" placeholder="排序方式">
              <el-option label="最新创建" value="created_at" />
              <el-option label="最新更新" value="updated_at" />
              <el-option label="最多浏览" value="views" />
              <el-option label="最多点赞" value="likes" />
            </el-select>
          </el-col>
          
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 食谱列表 -->
      <div class="recipes-section">
        <!-- 列表/网格切换 -->
        <div class="view-toggle">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="list">
              <el-icon><List /></el-icon>
              列表
            </el-radio-button>
            <el-radio-button label="grid">
              <el-icon><Grid /></el-icon>
              网格
            </el-radio-button>
          </el-radio-group>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>

        <!-- 食谱列表 -->
        <div v-else-if="recipes.length > 0" class="recipes-list">
          <!-- 列表视图 -->
          <div v-if="viewMode === 'list'" class="list-view">
            <div
              v-for="recipe in recipes"
              :key="recipe.id"
              class="recipe-item card"
            >
              <div class="recipe-content">
                <div class="recipe-image">
                  <el-image
                    :src="getFileUrl(recipe.coverImage) || '/default-recipe.jpg'"
                    :alt="recipe.title"
                    fit="cover"
                    class="cover-image"
                  />
                </div>
                
                <div class="recipe-info">
                  <div class="recipe-header">
                    <h3 class="recipe-title" @click="viewRecipe(recipe.id)">
                      {{ recipe.title }}
                    </h3>
                    <el-tag
                      :type="getStatusType(recipe.status)"
                      size="small"
                    >
                      {{ getStatusText(recipe.status) }}
                    </el-tag>
                  </div>
                  
                  <p class="recipe-description">{{ recipe.description }}</p>
                  
                  <div class="recipe-meta">
                    <div class="meta-left">
                      <span class="meta-item">
                        <el-icon><Timer /></el-icon>
                        {{ getTotalTime(recipe) }}分钟
                      </span>
                      <span class="meta-item">
                        <el-icon><User /></el-icon>
                        {{ recipe.servings }}人份
                      </span>
                      <span class="meta-item">
                        <el-icon><View /></el-icon>
                        {{ recipe.views || 0 }}次浏览
                      </span>
                    </div>
                    
                    <div class="meta-right">
                      <span class="update-time">
                        更新于 {{ formatDate(recipe.updatedAt) }}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div class="recipe-actions">
                  <el-button
                    size="small"
                    @click="viewRecipe(recipe.id)"
                  >
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  
                  <el-button
                    size="small"
                    @click="editRecipe(recipe.id)"
                  >
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  
                  <el-dropdown @command="(action: string) => handleAction(action, recipe)">
                    <el-button size="small">
                      更多
                      <el-icon><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          v-if="recipe.status === 1"
                          command="unpublish"
                        >
                          下架食谱
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="recipe.status !== 1"
                          command="publish"
                        >
                          发布食谱
                        </el-dropdown-item>
                        <el-dropdown-item command="duplicate">
                          复制食谱
                        </el-dropdown-item>
                        <el-dropdown-item divided command="delete">
                          <span style="color: #f56c6c">删除食谱</span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>

          <!-- 网格视图 -->
          <div v-else class="grid-view">
            <el-row :gutter="20">
              <el-col
                v-for="recipe in recipes"
                :key="recipe.id"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
                class="grid-item"
              >
                <div class="recipe-card card">
                  <div class="card-image">
                    <el-image
                      :src="getFileUrl(recipe.coverImage) || '/default-recipe.jpg'"
                      :alt="recipe.title"
                      fit="cover"
                      class="cover-image"
                    />
                    <div class="card-overlay">
                      <el-tag
                        :type="getStatusType(recipe.status)"
                        size="small"
                      >
                        {{ getStatusText(recipe.status) }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="card-content">
                    <h4 class="card-title" @click="viewRecipe(recipe.id)">
                      {{ recipe.title }}
                    </h4>
                    
                    <div class="card-meta">
                      <span>{{ recipe.views || 0 }}次浏览</span>
                      <span>{{ formatDate(recipe.updatedAt) }}</span>
                    </div>
                    
                    <div class="card-actions">
                      <el-button size="small" @click="editRecipe(recipe.id)">
                        编辑
                      </el-button>
                      <el-button size="small" @click="viewRecipe(recipe.id)">
                        查看
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-container">
          <el-empty description="您还没有创建任何食谱">
            <el-button type="primary" @click="$router.push('/recipe/create')">
              创建第一个食谱
            </el-button>
          </el-empty>
        </div>

        <!-- 分页 -->
        <div v-if="total > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[12, 24, 48]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Dish,
  Plus,
  Search,
  List,
  Grid,
  Timer,
  User,
  View,
  Edit,
  ArrowDown
} from '@element-plus/icons-vue'
import { recipesApi } from '@/api/recipes'
import { categoriesApi } from '@/api/categories'
import { getFileUrl } from '@/utils/file'
import type { Recipe, Category } from '@/types/recipe'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const recipes = ref<Recipe[]>([])
const categories = ref<Category[]>([])
const viewMode = ref<'list' | 'grid'>('list')
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref<number | null>(null)
const categoryFilter = ref<number | null>(null)
const sortBy = ref('created_at')

// 统计数据
const stats = reactive({
  total: 0,
  published: 0,
  drafts: 0,
  totalViews: 0
})

// 获取我的食谱列表
const fetchMyRecipes = async () => {
  loading.value = true
  try {
         // 调用后端API获取食谱列表
     const response = await recipesApi.getMyRecipes({
       page: currentPage.value,
       size: pageSize.value,
       keyword: searchKeyword.value || undefined,
       status: statusFilter.value || undefined,
       categoryId: categoryFilter.value || undefined,
       sortBy: sortBy.value,
       sortOrder: 'desc'
     })

    if (response.data) {
      recipes.value = response.data.content || []
      total.value = response.data.totalElements || 0
    }

    // 获取统计数据
    await fetchStats()

  } catch (error) {
    console.error('获取食谱列表失败:', error)
    ElMessage.error('获取食谱列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await categoriesApi.getActiveCategories()
    if (response.data) {
      categories.value = response.data.map(cat => ({
        ...cat,
        isEnabled: cat.status || 1
      }))
    }
  } catch (error) {
    console.error('获取分类失败:', error)
    // 使用备用数据
    categories.value = [
      { id: 1, name: '中式料理', isEnabled: 1 },
      { id: 2, name: '西式料理', isEnabled: 1 },
      { id: 3, name: '日式料理', isEnabled: 1 },
      { id: 4, name: '韩式料理', isEnabled: 1 },
      { id: 5, name: '甜品烘焙', isEnabled: 1 },
      { id: 6, name: '汤品饮品', isEnabled: 1 }
    ]
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await recipesApi.getMyRecipeStats()
    if (response.data) {
      Object.assign(stats, response.data)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

// 更新统计数据 (已被fetchStats替代，保留作为备用)
const updateStats = () => {
  stats.total = recipes.value.length
  stats.published = recipes.value.filter(r => r.status === 1).length
  stats.drafts = recipes.value.filter(r => r.status === 0).length
  stats.totalViews = recipes.value.reduce((sum, r) => sum + (r.views || 0), 0)
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchMyRecipes()
}

// 处理分页
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchMyRecipes()
}

const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchMyRecipes()
}

// 查看食谱
const viewRecipe = (id: number) => {
  router.push(`/recipe/${id}`)
}

// 编辑食谱
const editRecipe = (id: number) => {
  router.push(`/recipe/${id}/edit`)
}

// 处理食谱操作
const handleAction = async (action: string, recipe: Recipe) => {
  switch (action) {
    case 'publish':
      await publishRecipe(recipe)
      break
    case 'unpublish':
      await unpublishRecipe(recipe)
      break
    case 'duplicate':
      await duplicateRecipe(recipe)
      break
    case 'delete':
      await deleteRecipe(recipe)
      break
  }
}

// 发布食谱
const publishRecipe = async (recipe: Recipe) => {
  try {
    await recipesApi.publishRecipe(recipe.id)
    recipe.status = 1
    ElMessage.success('食谱发布成功')
    await fetchStats()
  } catch (error) {
    console.error('发布食谱失败:', error)
    ElMessage.error('发布失败')
  }
}

// 下架食谱
const unpublishRecipe = async (recipe: Recipe) => {
  try {
    await ElMessageBox.confirm('确定要下架这个食谱吗？', '确认下架', {
      type: 'warning'
    })
    
    await recipesApi.unpublishRecipe(recipe.id)
    recipe.status = 2
    ElMessage.success('食谱已下架')
    await fetchStats()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('下架食谱失败:', error)
      ElMessage.error('下架失败')
    }
  }
}

// 复制食谱
const duplicateRecipe = async (recipe: Recipe) => {
  try {
    const response = await recipesApi.duplicateRecipe(recipe.id)
    ElMessage.success('食谱复制成功，正在跳转到编辑页面...')
    if (response.data) {
      // 跳转到编辑页面
      router.push(`/recipe/${response.data.id}/edit`)
    }
  } catch (error) {
    console.error('复制食谱失败:', error)
    ElMessage.error('复制失败')
  }
}

// 删除食谱
const deleteRecipe = async (recipe: Recipe) => {
  try {
    await ElMessageBox.confirm('确定要删除这个食谱吗？删除后无法恢复。', '确认删除', {
      type: 'warning',
      confirmButtonText: '确定删除',
      confirmButtonClass: 'el-button--danger'
    })
    
    await recipesApi.deleteRecipe(recipe.id)
    
    // 从本地列表中移除
    const index = recipes.value.findIndex(r => r.id === recipe.id)
    if (index > -1) {
      recipes.value.splice(index, 1)
      total.value--
    }
    
    ElMessage.success('食谱删除成功')
    await fetchStats()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除食谱失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 工具函数
const getStatusType = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: 'info',    // 草稿
    1: 'success', // 已发布
    2: 'warning'  // 已下架
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '草稿',
    1: '已发布',
    2: '已下架'
  }
  return statusMap[status] || '未知'
}

const getTotalTime = (recipe: Recipe): number => {
  return (recipe.prepTime || 0) + (recipe.cookTime || 0)
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 组件挂载
onMounted(() => {
  fetchCategories()
  fetchMyRecipes()
})
</script>

<style scoped>
.my-recipes-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  padding: 20px 0;
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
  padding: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-subtitle {
  color: var(--text-secondary);
  margin: 0;
}

/* 统计信息 */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: var(--bg-light);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
  padding: 20px;
}

/* 视图切换 */
.view-toggle {
  margin-bottom: 20px;
  text-align: right;
}

/* 列表视图 */
.list-view {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recipe-item {
  padding: 0;
  overflow: hidden;
}

.recipe-content {
  display: grid;
  grid-template-columns: 150px 1fr auto;
  gap: 20px;
  padding: 20px;
  align-items: center;
}

.recipe-image {
  width: 150px;
  height: 100px;
}

.cover-image {
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-sm);
}

.recipe-info {
  flex: 1;
}

.recipe-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.recipe-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  cursor: pointer;
  transition: color 0.3s;
}

.recipe-title:hover {
  color: var(--primary-color);
}

.recipe-description {
  color: var(--text-secondary);
  margin: 0 0 15px;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-left {
  display: flex;
  gap: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.update-time {
  color: var(--text-light);
  font-size: 0.8rem;
}

.recipe-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 网格视图 */
.grid-view {
  margin-bottom: 20px;
}

.grid-item {
  margin-bottom: 20px;
}

.recipe-card {
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.recipe-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.card-image {
  position: relative;
  height: 180px;
}

.card-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
}

.card-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 15px;
  cursor: pointer;
  transition: color 0.3s;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-title:hover {
  color: var(--primary-color);
}

.card-meta {
  display: flex;
  justify-content: space-between;
  color: var(--text-secondary);
  font-size: 0.8rem;
  margin-bottom: 15px;
}

.card-actions {
  display: flex;
  gap: 8px;
  margin-top: auto;
}

.card-actions .el-button {
  flex: 1;
}

/* 空状态和分页 */
.empty-container,
.loading-container {
  padding: 60px 20px;
  text-align: center;
}

.pagination-container {
  margin-top: 30px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .recipe-content {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .recipe-actions {
    flex-direction: row;
    justify-content: space-around;
  }
  
  .meta-left {
    flex-direction: column;
    gap: 5px;
  }
  
  .recipe-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style> 