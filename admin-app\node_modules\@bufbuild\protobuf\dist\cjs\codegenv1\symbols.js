"use strict";
// Copyright 2021-2025 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.symbols = exports.wktPublicImportPaths = exports.packageName = void 0;
const symbols_js_1 = require("../codegenv2/symbols.js");
/**
 * @private
 */
exports.packageName = symbols_js_1.packageName;
/**
 * @private
 */
exports.wktPublicImportPaths = symbols_js_1.wktPublicImportPaths;
/**
 * @private
 */
// biome-ignore format: want this to read well
exports.symbols = Object.assign(Object.assign({}, symbols_js_1.symbols), { codegen: {
        boot: { typeOnly: false, bootstrapWktFrom: "../../codegenv1/boot.js", from: exports.packageName + "/codegenv1" },
        fileDesc: { typeOnly: false, bootstrapWktFrom: "../../codegenv1/file.js", from: exports.packageName + "/codegenv1" },
        enumDesc: { typeOnly: false, bootstrapWktFrom: "../../codegenv1/enum.js", from: exports.packageName + "/codegenv1" },
        extDesc: { typeOnly: false, bootstrapWktFrom: "../../codegenv1/extension.js", from: exports.packageName + "/codegenv1" },
        messageDesc: { typeOnly: false, bootstrapWktFrom: "../../codegenv1/message.js", from: exports.packageName + "/codegenv1" },
        serviceDesc: { typeOnly: false, bootstrapWktFrom: "../../codegenv1/service.js", from: exports.packageName + "/codegenv1" },
        tsEnum: { typeOnly: false, bootstrapWktFrom: "../../codegenv1/enum.js", from: exports.packageName + "/codegenv1" },
        GenFile: { typeOnly: true, bootstrapWktFrom: "../../codegenv1/types.js", from: exports.packageName + "/codegenv1" },
        GenEnum: { typeOnly: true, bootstrapWktFrom: "../../codegenv1/types.js", from: exports.packageName + "/codegenv1" },
        GenExtension: { typeOnly: true, bootstrapWktFrom: "../../codegenv1/types.js", from: exports.packageName + "/codegenv1" },
        GenMessage: { typeOnly: true, bootstrapWktFrom: "../../codegenv1/types.js", from: exports.packageName + "/codegenv1" },
        GenService: { typeOnly: true, bootstrapWktFrom: "../../codegenv1/types.js", from: exports.packageName + "/codegenv1" },
    } });
