import{d as ve,r as c,a as K,a3 as ce,o as fe,c as V,e as s,f as l,w as t,j as u,n as i,h as y,F as be,D as ge,W as Ve,S as ye,T as we,t as f,J as P,p as w,k as C,_ as Ce,a6 as ke,$ as q,U as Te,a1 as xe,N as je,C as Ue}from"./index-BDkHONMN.js";import{_ as De}from"./_plugin-vue_export-helper-DlAUqK2U.js";const $e={class:"email-template-container"},Ne={class:"toolbar"},ze={class:"toolbar-left"},Me={class:"card-header"},Ee={class:"template-list"},Oe=["onClick"],Le={class:"template-info"},Fe={class:"template-name"},Se={class:"template-code"},He={class:"template-meta"},qe={class:"template-actions"},Be={class:"card-header"},Ie={key:0},Je={key:0,class:"empty-state"},Re={key:1,class:"template-editor"},he={class:"form-tip"},Ae={class:"editor-container"},Ke={class:"visual-editor"},Pe={class:"html-editor"},We={class:"editor-tools"},Ge={class:"dialog-footer"},Qe={key:0,class:"preview-container"},Xe={class:"preview-header"},Ye={class:"preview-meta"},Ze=["innerHTML"],el={class:"dialog-footer"},ll=ve({__name:"EmailTemplateManager",setup(tl){const b=c([{id:1,name:"用户注册验证",code:"user_register",type:"register",subject:"欢迎注册{{siteName}} - 请验证您的邮箱",content:"亲爱的{{username}}，感谢您注册我们的服务...",htmlContent:"<html><body><h1>欢迎注册</h1></body></html>",description:"用户注册时发送的验证邮件",status:1,createTime:new Date},{id:2,name:"密码重置",code:"password_reset",type:"reset_password",subject:"{{siteName}} 密码重置请求",content:"您好{{username}}，您请求重置密码...",htmlContent:"<html><body><h1>密码重置</h1></body></html>",description:"用户忘记密码时发送的重置邮件",status:1,createTime:new Date}]),m=c(null),x=c(""),z=c("visual"),j=c(!1),M=c(!1),U=c(!1),B=c(),n=K({id:null,name:"",code:"",type:"",subject:"",content:"",htmlContent:"",description:"",status:1}),v=K({name:"",code:"",type:"",subject:""}),W={name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],code:[{required:!0,message:"请输入模板标识",trigger:"blur"}],type:[{required:!0,message:"请选择模板类型",trigger:"change"}],subject:[{required:!0,message:"请输入邮件主题",trigger:"blur"}]},k=c(null),$=c(""),E=c('{"username": "测试用户", "email": "<EMAIL>", "siteName": "食谱管理系统"}'),G=ce(()=>x.value?b.value.filter(o=>o.name.includes(x.value)||o.code.includes(x.value)):b.value),Q=()=>{},D=o=>{m.value=o,Object.assign(n,o)},X=o=>({register:"primary",reset_password:"warning",login_notice:"info",system_notice:"success",marketing:"danger"})[o]||"info",Y=()=>{Object.assign(v,{name:"",code:"",type:"",subject:""}),j.value=!0},Z=async()=>{var o;try{await((o=B.value)==null?void 0:o.validate());const e={id:Date.now(),...v,content:"",htmlContent:"",description:"",status:1,createTime:new Date};b.value.push(e),j.value=!1,w.success("模板创建成功"),D(e)}catch{}},ee=o=>{D(o)},le=o=>{const e={...o,id:Date.now(),name:`${o.name}_副本`,code:`${o.code}_copy`,createTime:new Date};b.value.push(e),w.success("模板复制成功"),D(e)},te=async o=>{var e;try{await Ue.confirm(`确定要删除模板"${o.name}"吗？`,"提示",{type:"warning"});const r=b.value.findIndex(d=>d.id===o.id);r>-1&&(b.value.splice(r,1),w.success("删除成功"),((e=m.value)==null?void 0:e.id)===o.id&&(m.value=null))}catch{}},ae=()=>{if(!m.value)return;const o=b.value.findIndex(e=>e.id===m.value.id);o>-1&&(Object.assign(b.value[o],n),w.success("模板保存成功"))},oe=()=>{m.value&&Object.assign(n,m.value)},N=o=>{const e=`{{${o}}}`;z.value==="visual"?n.content+=e:n.htmlContent+=e},se=()=>{if(!m.value)return;const o={username:"张三",email:"<EMAIL>",siteName:"食谱管理系统",date:new Date().toLocaleDateString()};let e=n.content||n.htmlContent;Object.keys(o).forEach(r=>{const d=new RegExp(`{{${r}}}`,"g");e=e.replace(d,o[r])}),k.value={name:n.name,subject:n.subject.replace(/{{(\w+)}}/g,(r,d)=>o[d]||r),type:n.type,renderedContent:e},M.value=!0},ne=()=>{m.value&&($.value="",U.value=!0)},de=()=>{if(!$.value){w.warning("请输入收件人邮箱");return}try{JSON.parse(E.value)}catch{w.error("测试数据格式错误，请输入有效的JSON");return}w.success("测试邮件发送成功"),U.value=!1};return fe(()=>{b.value.length>0&&D(b.value[0])}),(o,e)=>{const r=u("el-icon"),d=u("el-button"),_=u("el-input"),I=u("el-tag"),O=u("el-dropdown-item"),ue=u("el-dropdown-menu"),ie=u("el-dropdown"),J=u("el-card"),T=u("el-col"),re=u("el-empty"),p=u("el-form-item"),L=u("el-row"),g=u("el-option"),R=u("el-select"),me=u("el-switch"),h=u("el-tab-pane"),pe=u("el-tabs"),_e=u("el-button-group"),F=u("el-form"),S=u("el-dialog");return C(),V("div",$e,[e[44]||(e[44]=s("div",{class:"page-header"},[s("h1",null,"邮件模板管理"),s("p",null,"管理系统邮件模板，自定义邮件内容")],-1)),s("div",Ne,[s("div",ze,[l(d,{type:"primary",onClick:Y},{default:t(()=>[l(r,null,{default:t(()=>[l(y(be))]),_:1}),e[25]||(e[25]=i(" 添加模板 "))]),_:1,__:[25]}),l(d,{onClick:se,disabled:m.value===null},{default:t(()=>[l(r,null,{default:t(()=>[l(y(ge))]),_:1}),e[26]||(e[26]=i(" 预览模板 "))]),_:1,__:[26]},8,["disabled"]),l(d,{onClick:ne,disabled:m.value===null},{default:t(()=>[l(r,null,{default:t(()=>[l(y(Ve))]),_:1}),e[27]||(e[27]=i(" 测试发送 "))]),_:1,__:[27]},8,["disabled"])])]),l(L,{gutter:20},{default:t(()=>[l(T,{span:8},{default:t(()=>[l(J,null,{header:t(()=>[s("div",Me,[e[28]||(e[28]=s("span",null,"模板列表",-1)),l(_,{modelValue:x.value,"onUpdate:modelValue":e[0]||(e[0]=a=>x.value=a),placeholder:"搜索模板",size:"small",clearable:"",onInput:Q},null,8,["modelValue"])])]),default:t(()=>[s("div",Ee,[(C(!0),V(ye,null,we(G.value,a=>{var A;return C(),V("div",{key:a.id,class:Ce(["template-item",{active:((A=m.value)==null?void 0:A.id)===a.id}]),onClick:H=>D(a)},[s("div",Le,[s("div",Fe,f(a.name),1),s("div",Se,f(a.code),1),s("div",He,[l(I,{type:X(a.type),size:"small"},{default:t(()=>[i(f(a.type),1)]),_:2},1032,["type"]),l(I,{type:a.status===1?"success":"danger",size:"small"},{default:t(()=>[i(f(a.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])])]),s("div",qe,[l(ie,{trigger:"click"},{dropdown:t(()=>[l(ue,null,{default:t(()=>[l(O,{onClick:q(H=>ee(a),["stop"])},{default:t(()=>[l(r,null,{default:t(()=>[l(y(Te))]),_:1}),e[29]||(e[29]=i(" 编辑 "))]),_:2,__:[29]},1032,["onClick"]),l(O,{onClick:q(H=>le(a),["stop"])},{default:t(()=>[l(r,null,{default:t(()=>[l(y(xe))]),_:1}),e[30]||(e[30]=i(" 复制 "))]),_:2,__:[30]},1032,["onClick"]),l(O,{onClick:q(H=>te(a),["stop"]),divided:""},{default:t(()=>[l(r,null,{default:t(()=>[l(y(je))]),_:1}),e[31]||(e[31]=i(" 删除 "))]),_:2,__:[31]},1032,["onClick"])]),_:2},1024)]),default:t(()=>[l(d,{type:"text",size:"small"},{default:t(()=>[l(r,null,{default:t(()=>[l(y(ke))]),_:1})]),_:1})]),_:2},1024)])],10,Oe)}),128))])]),_:1})]),_:1}),l(T,{span:16},{default:t(()=>[l(J,null,{header:t(()=>[s("div",Be,[e[34]||(e[34]=s("span",null,"模板编辑",-1)),m.value?(C(),V("div",Ie,[l(d,{onClick:ae,type:"primary",size:"small"},{default:t(()=>e[32]||(e[32]=[i(" 保存模板 ")])),_:1,__:[32]}),l(d,{onClick:oe,size:"small"},{default:t(()=>e[33]||(e[33]=[i(" 重置 ")])),_:1,__:[33]})])):P("",!0)])]),default:t(()=>[m.value?(C(),V("div",Re,[l(F,{model:n,"label-width":"100px"},{default:t(()=>[l(L,{gutter:20},{default:t(()=>[l(T,{span:12},{default:t(()=>[l(p,{label:"模板名称"},{default:t(()=>[l(_,{modelValue:n.name,"onUpdate:modelValue":e[1]||(e[1]=a=>n.name=a),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(T,{span:12},{default:t(()=>[l(p,{label:"模板标识"},{default:t(()=>[l(_,{modelValue:n.code,"onUpdate:modelValue":e[2]||(e[2]=a=>n.code=a),placeholder:"请输入模板标识"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(L,{gutter:20},{default:t(()=>[l(T,{span:12},{default:t(()=>[l(p,{label:"模板类型"},{default:t(()=>[l(R,{modelValue:n.type,"onUpdate:modelValue":e[3]||(e[3]=a=>n.type=a),placeholder:"请选择类型"},{default:t(()=>[l(g,{label:"注册验证",value:"register"}),l(g,{label:"密码重置",value:"reset_password"}),l(g,{label:"登录通知",value:"login_notice"}),l(g,{label:"系统通知",value:"system_notice"}),l(g,{label:"营销推广",value:"marketing"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(T,{span:12},{default:t(()=>[l(p,{label:"状态"},{default:t(()=>[l(me,{modelValue:n.status,"onUpdate:modelValue":e[4]||(e[4]=a=>n.status=a),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(p,{label:"邮件主题"},{default:t(()=>[l(_,{modelValue:n.subject,"onUpdate:modelValue":e[5]||(e[5]=a=>n.subject=a),placeholder:"请输入邮件主题"},null,8,["modelValue"]),s("div",he," 支持变量："+f(o.username)+", "+f(o.email)+", "+f(o.siteName)+", "+f(o.date)+" 等 ",1)]),_:1}),l(p,{label:"邮件内容"},{default:t(()=>[s("div",Ae,[l(pe,{modelValue:z.value,"onUpdate:modelValue":e[8]||(e[8]=a=>z.value=a),type:"border-card"},{default:t(()=>[l(h,{label:"可视化编辑",name:"visual"},{default:t(()=>[s("div",Ke,[l(_,{modelValue:n.content,"onUpdate:modelValue":e[6]||(e[6]=a=>n.content=a),type:"textarea",rows:15,placeholder:"请输入邮件内容，支持HTML格式"},null,8,["modelValue"])])]),_:1}),l(h,{label:"HTML源码",name:"html"},{default:t(()=>[s("div",Pe,[l(_,{modelValue:n.htmlContent,"onUpdate:modelValue":e[7]||(e[7]=a=>n.htmlContent=a),type:"textarea",rows:15,placeholder:"请输入HTML源码"},null,8,["modelValue"])])]),_:1})]),_:1},8,["modelValue"])]),s("div",We,[l(_e,null,{default:t(()=>[l(d,{size:"small",onClick:e[9]||(e[9]=a=>N("username"))},{default:t(()=>e[35]||(e[35]=[i(" 插入用户名 ")])),_:1,__:[35]}),l(d,{size:"small",onClick:e[10]||(e[10]=a=>N("email"))},{default:t(()=>e[36]||(e[36]=[i(" 插入邮箱 ")])),_:1,__:[36]}),l(d,{size:"small",onClick:e[11]||(e[11]=a=>N("siteName"))},{default:t(()=>e[37]||(e[37]=[i(" 插入站点名 ")])),_:1,__:[37]}),l(d,{size:"small",onClick:e[12]||(e[12]=a=>N("date"))},{default:t(()=>e[38]||(e[38]=[i(" 插入日期 ")])),_:1,__:[38]})]),_:1})])]),_:1}),l(p,{label:"备注"},{default:t(()=>[l(_,{modelValue:n.description,"onUpdate:modelValue":e[13]||(e[13]=a=>n.description=a),type:"textarea",placeholder:"请输入模板描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])):(C(),V("div",Je,[l(re,{description:"请选择一个模板进行编辑"})]))]),_:1})]),_:1})]),_:1}),l(S,{modelValue:j.value,"onUpdate:modelValue":e[19]||(e[19]=a=>j.value=a),title:"添加邮件模板",width:"600px"},{footer:t(()=>[s("div",Ge,[l(d,{onClick:e[18]||(e[18]=a=>j.value=!1)},{default:t(()=>e[39]||(e[39]=[i("取消")])),_:1,__:[39]}),l(d,{type:"primary",onClick:Z},{default:t(()=>e[40]||(e[40]=[i("确定")])),_:1,__:[40]})])]),default:t(()=>[l(F,{ref_key:"templateFormRef",ref:B,model:v,rules:W,"label-width":"100px"},{default:t(()=>[l(p,{label:"模板名称",prop:"name"},{default:t(()=>[l(_,{modelValue:v.name,"onUpdate:modelValue":e[14]||(e[14]=a=>v.name=a),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1}),l(p,{label:"模板标识",prop:"code"},{default:t(()=>[l(_,{modelValue:v.code,"onUpdate:modelValue":e[15]||(e[15]=a=>v.code=a),placeholder:"请输入唯一标识"},null,8,["modelValue"])]),_:1}),l(p,{label:"模板类型",prop:"type"},{default:t(()=>[l(R,{modelValue:v.type,"onUpdate:modelValue":e[16]||(e[16]=a=>v.type=a),placeholder:"请选择类型"},{default:t(()=>[l(g,{label:"注册验证",value:"register"}),l(g,{label:"密码重置",value:"reset_password"}),l(g,{label:"登录通知",value:"login_notice"}),l(g,{label:"系统通知",value:"system_notice"}),l(g,{label:"营销推广",value:"marketing"})]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"邮件主题",prop:"subject"},{default:t(()=>[l(_,{modelValue:v.subject,"onUpdate:modelValue":e[17]||(e[17]=a=>v.subject=a),placeholder:"请输入邮件主题"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(S,{modelValue:M.value,"onUpdate:modelValue":e[20]||(e[20]=a=>M.value=a),title:"模板预览",width:"800px"},{default:t(()=>[k.value?(C(),V("div",Qe,[s("div",Xe,[s("h3",null,f(k.value.subject),1),s("div",Ye,[s("span",null,"模板："+f(k.value.name),1),s("span",null,"类型："+f(k.value.type),1)])]),s("div",{class:"preview-content",innerHTML:k.value.renderedContent},null,8,Ze)])):P("",!0)]),_:1},8,["modelValue"]),l(S,{modelValue:U.value,"onUpdate:modelValue":e[24]||(e[24]=a=>U.value=a),title:"测试发送邮件",width:"500px"},{footer:t(()=>[s("div",el,[l(d,{onClick:e[23]||(e[23]=a=>U.value=!1)},{default:t(()=>e[42]||(e[42]=[i("取消")])),_:1,__:[42]}),l(d,{type:"primary",onClick:de},{default:t(()=>e[43]||(e[43]=[i("发送测试")])),_:1,__:[43]})])]),default:t(()=>[l(F,{"label-width":"100px"},{default:t(()=>[l(p,{label:"收件人邮箱"},{default:t(()=>[l(_,{modelValue:$.value,"onUpdate:modelValue":e[21]||(e[21]=a=>$.value=a),placeholder:"请输入测试邮箱地址"},null,8,["modelValue"])]),_:1}),l(p,{label:"测试数据"},{default:t(()=>[l(_,{modelValue:E.value,"onUpdate:modelValue":e[22]||(e[22]=a=>E.value=a),type:"textarea",placeholder:'{"username": "测试用户", "email": "<EMAIL>"}',rows:4},null,8,["modelValue"]),e[41]||(e[41]=s("div",{class:"form-tip"}," JSON格式的测试数据，用于替换模板变量 ",-1))]),_:1,__:[41]})]),_:1})]),_:1},8,["modelValue"])])}}}),sl=De(ll,[["__scopeId","data-v-f9513b46"]]);export{sl as default};
