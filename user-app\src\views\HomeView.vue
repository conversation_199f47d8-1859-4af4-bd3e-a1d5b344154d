<template>
  <div class="home-view">
    <div class="page-container">
      <div class="welcome-section">
        <h1 class="welcome-title">欢迎来到美食食谱</h1>
        <p class="welcome-subtitle">分享你的私家美味，发现生活的美好</p>
        
        <div class="action-buttons">
          <el-button type="primary" size="large" @click="$router.push('/recipes')">
            <el-icon><Search /></el-icon>
            浏览食谱
          </el-button>
          <el-button v-if="isLoggedIn" size="large" @click="$router.push('/recipe/create')">
            <el-icon><Plus /></el-icon>
            创建食谱
          </el-button>
          <el-button v-else size="large" @click="$router.push('/auth/register')">
            立即注册
          </el-button>
        </div>
      </div>
      
      <div class="features-section">
        <div class="feature-card card" @click="$router.push('/recipes')">
          <div class="feature-icon">🍳</div>
          <h3>浏览食谱</h3>
          <p>发现各种美味食谱，学习新的烹饪技巧</p>
        </div>
        
        <div class="feature-card card" @click="handleCreateRecipe">
          <div class="feature-icon">📝</div>
          <h3>创建食谱</h3>
          <p>记录你的独家秘方，分享烹饪心得</p>
        </div>
        
        <div class="feature-card card" @click="handleFavorites">
          <div class="feature-icon">💖</div>
          <h3>我的收藏</h3>
          <p>管理你的食谱收藏和创作</p>
        </div>
      </div>

      <!-- 热门食谱预览 -->
      <div class="preview-section">
        <h2 class="section-title">热门食谱</h2>
        <div class="recipe-preview">
          <el-loading :loading="loading" style="min-height: 200px;">
            <el-row v-if="popularRecipes.length > 0" :gutter="20">
              <el-col v-for="recipe in popularRecipes" :key="recipe.id" :xs="24" :sm="12" :md="8" :lg="6">
                <div class="preview-card card" @click="handleRecipeClick(recipe)">
                  <div class="preview-image">
                    <el-image
                      :src="getFileUrl(recipe.coverImage) || '/default-recipe.jpg'"
                      :alt="recipe.title"
                      fit="cover"
                    />
                    <div class="recipe-stats">
                      <span><el-icon><View /></el-icon> {{ recipe.views }}</span>
                      <span><el-icon><Star /></el-icon> {{ recipe.likes }}</span>
                    </div>
                  </div>
                  <div class="preview-content">
                    <h4 class="recipe-title">{{ recipe.title }}</h4>
                    <p class="recipe-desc">{{ recipe.description }}</p>
                    <div class="recipe-meta">
                      <span class="time">{{ getTotalTime(recipe) }}分钟</span>
                      <span class="difficulty">{{ getDifficultyText(recipe.difficulty) }}</span>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
            <div v-else-if="!loading" class="empty-state">
              <el-empty description="暂无热门食谱" />
            </div>
          </el-loading>
        </div>
        <div class="preview-more">
          <el-button type="primary" @click="$router.push('/recipes/popular')">
            查看热门排行榜
          </el-button>
          <el-button @click="$router.push('/recipes')">
            查看更多食谱
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Search,
  Plus,
  View,
  Star
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import type { Recipe } from '@/types/recipe'
import { recipesApi } from '@/api/recipes'
import { getFileUrl } from '@/utils/file'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const popularRecipes = ref<Recipe[]>([])
const loading = ref(false)

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)
const userInfo = computed(() => userStore.userInfo)

// 获取热门食谱
const fetchPopularRecipes = async () => {
  try {
    loading.value = true
    const response = await recipesApi.getPopularRecipes(4)
    popularRecipes.value = response.data || []
  } catch (error) {
    console.error('获取热门食谱失败:', error)
    ElMessage.error('获取热门食谱失败')
    // 如果API请求失败，使用备用数据
    popularRecipes.value = [{
        id: 1,
        title: '红烧肉',
        description: '经典家常菜，肥而不腻',
        coverImage: '/recipe1.jpg',
        prepTime: 30,
        cookTime: 60,
        difficulty: 2,
        servings: 4,
        views: 1234,
        likes: 89,
        favorites: 56,
        categoryId: 1,
        creatorId: 1,
        isPublic: 1,
        status: 1,
        createdAt: '2024-01-15',
        updatedAt: '2024-01-15'
      },
      {
        id: 2,
        title: '蛋炒饭',
        description: '简单易学的快手菜',
        coverImage: '/recipe2.jpg',
        prepTime: 10,
        cookTime: 15,
        difficulty: 1,
        servings: 2,
        views: 856,
        likes: 67,
        favorites: 34,
        categoryId: 1,
        creatorId: 2,
        isPublic: 1,
        status: 1,
        createdAt: '2024-01-14',
        updatedAt: '2024-01-14'
      },
      {
        id: 3,
        title: '糖醋里脊',
        description: '酸甜可口的经典菜',
        coverImage: '/recipe3.jpg',
        prepTime: 20,
        cookTime: 25,
        difficulty: 2,
        servings: 3,
        views: 743,
        likes: 52,
        favorites: 28,
        categoryId: 1,
        creatorId: 3,
        isPublic: 1,
        status: 1,
        createdAt: '2024-01-13',
        updatedAt: '2024-01-13'
      },
      {
        id: 4,
        title: '提拉米苏',
        description: '意式经典甜品',
        coverImage: '/recipe4.jpg',
        prepTime: 45,
        cookTime: 0,
        difficulty: 3,
        servings: 6,
        views: 987,
        likes: 124,
        favorites: 89,
        categoryId: 5,
        creatorId: 4,
        isPublic: 1,
        status: 1,
        createdAt: '2024-01-12',
        updatedAt: '2024-01-12'
      }]
  } finally {
    loading.value = false
  }
}



// 处理创建食谱
const handleCreateRecipe = () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录')
    router.push('/auth/login')
    return
  }
  router.push('/recipe/create')
}

// 处理我的食谱
const handleMyRecipes = () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录')
    router.push('/auth/login')
    return
  }
  router.push('/my-recipes')
}

// 处理我的收藏
const handleFavorites = () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录')
    router.push('/auth/login')
    return
  }
  router.push('/favorites')
}

// 获取总时间
const getTotalTime = (recipe: Recipe): number => {
  return (recipe.prepTime || 0) + (recipe.cookTime || 0)
}

// 获取难度文本
const getDifficultyText = (difficulty: number): string => {
  const difficultyMap: Record<number, string> = {
    1: '简单',
    2: '中等',
    3: '困难'
  }
  return difficultyMap[difficulty] || '未知'
}

// 处理食谱卡片点击
const handleRecipeClick = async (recipe: Recipe) => {
  try {
    // 增加浏览量
    await recipesApi.incrementViews(recipe.id)
    // 跳转到详情页
    router.push(`/recipe/${recipe.id}`)
  } catch (error) {
    console.error('增加浏览量失败:', error)
    // 即使增加浏览量失败，也要跳转到详情页
    router.push(`/recipe/${recipe.id}`)
  }
}

// 组件挂载
onMounted(() => {
  fetchPopularRecipes()
})
</script>

<style scoped>
.home-view {
  padding: 0;
}





/* 欢迎区域 */
.welcome-section {
  text-align: center;
  padding: 80px 40px;
}

.welcome-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  color: var(--text-secondary);
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 2rem;
}

.action-buttons .el-button {
  padding: 15px 30px;
  font-size: 1rem;
}

/* 功能卡片 */
.features-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  padding: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  text-align: center;
  padding: 40px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.feature-card:hover {
  transform: translateY(-5px);
  border-color: var(--primary-color);
  box-shadow: 0 15px 30px rgba(255, 154, 162, 0.2);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 预览区域 */
.preview-section {
  padding: 60px 40px;
  max-width: 1400px;
  margin: 0 auto;
}

.section-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 40px;
}

.recipe-preview {
  margin-bottom: 40px;
}

.preview-card {
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.preview-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.preview-image .el-image {
  width: 100%;
  height: 100%;
}

.recipe-stats {
  position: absolute;
  bottom: 10px;
  left: 10px;
  display: flex;
  gap: 15px;
}

.recipe-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
  color: white;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.6);
  padding: 4px 8px;
  border-radius: 12px;
}

.preview-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.recipe-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recipe-desc {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 15px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.time, .difficulty {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.preview-more {
  text-align: center;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    padding: 60px 20px;
  }
  
  .welcome-title {
    font-size: 2.5rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .action-buttons .el-button {
    width: 250px;
  }
  
  .features-section {
    grid-template-columns: 1fr;
    padding: 20px;
  }
  
  .preview-section {
    padding: 40px 20px;
  }
}
</style> 