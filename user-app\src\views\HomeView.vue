<template>
  <div class="home-view">
    <!-- 欢迎横幅 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">欢迎来到美食食谱</h1>
        <p class="hero-subtitle">分享你的私家美味，发现生活的美好</p>

        <!-- 搜索框 -->
        <div class="search-container">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索食谱、食材或创作者..."
            size="large"
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>

        <!-- 热门搜索词 -->
        <div v-if="hotKeywords.length > 0" class="hot-keywords">
          <span class="hot-label">热门搜索：</span>
          <el-tag
            v-for="keyword in hotKeywords.slice(0, 8)"
            :key="keyword"
            class="keyword-tag"
            @click="searchKeyword = keyword; handleSearch()"
          >
            {{ keyword }}
          </el-tag>
        </div>

        <div class="action-buttons">
          <el-button type="primary" size="large" @click="$router.push('/recipes')">
            <el-icon><Search /></el-icon>
            浏览食谱
          </el-button>
          <el-button v-if="isLoggedIn" size="large" @click="$router.push('/recipe/create')">
            <el-icon><Plus /></el-icon>
            创建食谱
          </el-button>
          <el-button v-else size="large" @click="$router.push('/auth/register')">
            立即注册
          </el-button>
        </div>
      </div>
    </div>

    <div class="page-container">
      <!-- 统计数据展示 -->
      <div class="stats-section">
        <el-loading :loading="statsLoading">
          <div class="stats-grid">
            <!-- 全站统计 -->
            <div class="stats-card global-stats">
              <h3 class="stats-title">
                <el-icon><DataAnalysis /></el-icon>
                全站数据
              </h3>
              <div class="stats-content">
                <div class="stat-item">
                  <div class="stat-number">{{ formatNumber(homeStats?.globalStats?.totalRecipes || 0) }}</div>
                  <div class="stat-label">食谱总数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ formatNumber(homeStats?.globalStats?.totalUsers || 0) }}</div>
                  <div class="stat-label">用户总数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ formatNumber(homeStats?.globalStats?.totalViews || 0) }}</div>
                  <div class="stat-label">总浏览量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ formatNumber(homeStats?.globalStats?.totalLikes || 0) }}</div>
                  <div class="stat-label">总点赞数</div>
                </div>
              </div>
            </div>

            <!-- 个人统计（仅登录用户） -->
            <div v-if="isLoggedIn && homeStats?.userStats" class="stats-card user-stats">
              <h3 class="stats-title">
                <el-icon><User /></el-icon>
                我的数据
              </h3>
              <div class="stats-content">
                <div class="stat-item">
                  <div class="stat-number">{{ homeStats.userStats.myRecipeCount }}</div>
                  <div class="stat-label">我的食谱</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ homeStats.userStats.myFavoriteCount }}</div>
                  <div class="stat-label">我的收藏</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ homeStats.userStats.myFollowersCount }}</div>
                  <div class="stat-label">粉丝数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ formatNumber(homeStats.userStats.myTotalViews) }}</div>
                  <div class="stat-label">总浏览量</div>
                </div>
              </div>
            </div>

            <!-- 今日数据 -->
            <div class="stats-card today-stats">
              <h3 class="stats-title">
                <el-icon><Calendar /></el-icon>
                今日数据
              </h3>
              <div class="stats-content">
                <div class="stat-item">
                  <div class="stat-number">{{ homeStats?.globalStats?.todayNewRecipes || 0 }}</div>
                  <div class="stat-label">新增食谱</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ homeStats?.globalStats?.todayNewUsers || 0 }}</div>
                  <div class="stat-label">新增用户</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ formatNumber(homeStats?.globalStats?.activeUsers || 0) }}</div>
                  <div class="stat-label">活跃用户</div>
                </div>
              </div>
            </div>
          </div>
        </el-loading>
      </div>

      <!-- 最新食谱 -->
      <div class="content-section">
        <div class="section-header">
          <h2 class="section-title">
            <el-icon><Clock /></el-icon>
            最新发布
          </h2>
          <el-button type="primary" link @click="$router.push('/recipes')">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <el-loading :loading="latestLoading">
          <div v-if="latestRecipes.length > 0" class="recipe-grid">
            <div
              v-for="recipe in latestRecipes"
              :key="recipe.id"
              class="recipe-card"
              @click="handleRecipeClick(recipe)"
            >
              <div class="recipe-image">
                <el-image
                  :src="getFileUrl(recipe.coverImage) || '/default-recipe.jpg'"
                  :alt="recipe.title"
                  fit="cover"
                />
                <div class="recipe-overlay">
                  <div class="recipe-stats">
                    <span><el-icon><View /></el-icon> {{ formatNumber(recipe.views) }}</span>
                    <span><el-icon><Star /></el-icon> {{ formatNumber(recipe.likes) }}</span>
                  </div>
                  <div class="recipe-difficulty">{{ getDifficultyText(recipe.difficulty) }}</div>
                </div>
              </div>
              <div class="recipe-content">
                <h4 class="recipe-title">{{ recipe.title }}</h4>
                <p class="recipe-desc">{{ recipe.description }}</p>
                <div class="recipe-meta">
                  <div class="creator-info">
                    <el-avatar :size="24" :src="getFileUrl(recipe.creatorAvatar)">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                    <span class="creator-name">{{ recipe.creatorNickname || recipe.creatorUsername }}</span>
                  </div>
                  <div class="recipe-time">
                    <el-icon><Timer /></el-icon>
                    {{ getTotalTime(recipe) }}分钟
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="!latestLoading" class="empty-state">
            <el-empty description="暂无最新食谱" />
          </div>
        </el-loading>
      </div>

      <!-- 推荐食谱 -->
      <div class="content-section">
        <div class="section-header">
          <h2 class="section-title">
            <el-icon><Star /></el-icon>
            {{ isLoggedIn ? '为你推荐' : '热门推荐' }}
          </h2>
          <el-button type="primary" link @click="$router.push('/recipes/popular')">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <el-loading :loading="recommendedLoading">
          <div v-if="recommendedRecipes.length > 0" class="recipe-grid">
            <div
              v-for="recipe in recommendedRecipes"
              :key="recipe.id"
              class="recipe-card"
              @click="handleRecipeClick(recipe)"
            >
              <div class="recipe-image">
                <el-image
                  :src="getFileUrl(recipe.coverImage) || '/default-recipe.jpg'"
                  :alt="recipe.title"
                  fit="cover"
                />
                <div class="recipe-overlay">
                  <div class="recipe-stats">
                    <span><el-icon><View /></el-icon> {{ formatNumber(recipe.views) }}</span>
                    <span><el-icon><Star /></el-icon> {{ formatNumber(recipe.likes) }}</span>
                  </div>
                  <div class="recipe-difficulty">{{ getDifficultyText(recipe.difficulty) }}</div>
                </div>
              </div>
              <div class="recipe-content">
                <h4 class="recipe-title">{{ recipe.title }}</h4>
                <p class="recipe-desc">{{ recipe.description }}</p>
                <div class="recipe-meta">
                  <div class="creator-info">
                    <el-avatar :size="24" :src="getFileUrl(recipe.creatorAvatar)">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                    <span class="creator-name">{{ recipe.creatorNickname || recipe.creatorUsername }}</span>
                  </div>
                  <div class="recipe-time">
                    <el-icon><Timer /></el-icon>
                    {{ getTotalTime(recipe) }}分钟
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="!recommendedLoading" class="empty-state">
            <el-empty description="暂无推荐食谱" />
          </div>
        </el-loading>
      </div>

      <!-- 热门创作者 -->
      <div class="content-section">
        <div class="section-header">
          <h2 class="section-title">
            <el-icon><UserFilled /></el-icon>
            热门创作者
          </h2>
          <el-button type="primary" link @click="$router.push('/users')">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <el-loading :loading="creatorsLoading">
          <div v-if="popularCreators.length > 0" class="creators-grid">
            <div
              v-for="creator in popularCreators"
              :key="creator.id"
              class="creator-card"
              @click="$router.push(`/user/${creator.id}`)"
            >
              <div class="creator-header">
                <el-avatar :size="60" :src="getFileUrl(creator.avatar)">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <div class="creator-info">
                  <h4 class="creator-name">{{ creator.nickname || creator.username }}</h4>
                  <p class="creator-bio">{{ creator.bio || '这个人很懒，什么都没留下' }}</p>
                </div>
              </div>
              <div class="creator-stats">
                <div class="stat-item">
                  <div class="stat-number">{{ creator.recipeCount }}</div>
                  <div class="stat-label">食谱</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ formatNumber(creator.totalViews) }}</div>
                  <div class="stat-label">浏览</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ creator.followersCount }}</div>
                  <div class="stat-label">粉丝</div>
                </div>
              </div>
              <div v-if="creator.latestRecipeTitle" class="latest-recipe">
                <div class="latest-recipe-cover">
                  <el-image
                    :src="getFileUrl(creator.latestRecipeCover) || '/default-recipe.jpg'"
                    fit="cover"
                  />
                </div>
                <div class="latest-recipe-info">
                  <div class="latest-recipe-title">{{ creator.latestRecipeTitle }}</div>
                  <div class="latest-recipe-time">{{ formatTime(creator.latestPublishTime) }}</div>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="!creatorsLoading" class="empty-state">
            <el-empty description="暂无热门创作者" />
          </div>
        </el-loading>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Search,
  Plus,
  View,
  Star,
  Timer,
  User,
  UserFilled,
  Clock,
  ArrowRight,
  DataAnalysis,
  Calendar
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import type { Recipe } from '@/types/recipe'
import { recipesApi } from '@/api/recipes'
import { homeApi, type HomeStats, type LatestRecipe, type PopularCreator } from '@/api/home'
import { getFileUrl } from '@/utils/file'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const searchKeyword = ref('')
const hotKeywords = ref<string[]>([])
const homeStats = ref<HomeStats | null>(null)
const latestRecipes = ref<LatestRecipe[]>([])
const recommendedRecipes = ref<LatestRecipe[]>([])
const popularCreators = ref<PopularCreator[]>([])

// 加载状态
const statsLoading = ref(false)
const latestLoading = ref(false)
const recommendedLoading = ref(false)
const creatorsLoading = ref(false)

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)
const userInfo = computed(() => userStore.userInfo)

// 获取首页统计数据
const fetchHomeStats = async () => {
  try {
    statsLoading.value = true
    homeStats.value = await homeApi.getHomeStats()
  } catch (error) {
    console.error('获取首页统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

// 获取最新食谱
const fetchLatestRecipes = async () => {
  try {
    latestLoading.value = true
    latestRecipes.value = await homeApi.getLatestRecipes(8)
  } catch (error) {
    console.error('获取最新食谱失败:', error)
    ElMessage.error('获取最新食谱失败')
  } finally {
    latestLoading.value = false
  }
}

// 获取推荐食谱
const fetchRecommendedRecipes = async () => {
  try {
    recommendedLoading.value = true
    recommendedRecipes.value = await homeApi.getRecommendedRecipes(8)
  } catch (error) {
    console.error('获取推荐食谱失败:', error)
    ElMessage.error('获取推荐食谱失败')
  } finally {
    recommendedLoading.value = false
  }
}

// 获取热门创作者
const fetchPopularCreators = async () => {
  try {
    creatorsLoading.value = true
    popularCreators.value = await homeApi.getPopularCreators(6)
  } catch (error) {
    console.error('获取热门创作者失败:', error)
    ElMessage.error('获取热门创作者失败')
  } finally {
    creatorsLoading.value = false
  }
}

// 获取热门搜索关键词
const fetchHotKeywords = async () => {
  try {
    hotKeywords.value = await homeApi.getHotSearchKeywords(10)
  } catch (error) {
    console.error('获取热门搜索关键词失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  router.push({
    path: '/search',
    query: { keyword: searchKeyword.value.trim() }
  })
}

// 数字格式化
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 时间格式化
const formatTime = (timeStr: string): string => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString()
  }
}

// 获取总时间
const getTotalTime = (recipe: LatestRecipe): number => {
  return (recipe.prepTime || 0) + (recipe.cookTime || 0)
}

// 获取难度文本
const getDifficultyText = (difficulty: number): string => {
  const difficultyMap: Record<number, string> = {
    1: '简单',
    2: '中等',
    3: '困难'
  }
  return difficultyMap[difficulty] || '未知'
}

// 处理食谱卡片点击
const handleRecipeClick = async (recipe: LatestRecipe) => {
  try {
    // 增加浏览量
    await recipesApi.incrementViews(recipe.id)
    // 跳转到详情页
    router.push(`/recipe/${recipe.id}`)
  } catch (error) {
    console.error('增加浏览量失败:', error)
    // 即使增加浏览量失败，也要跳转到详情页
    router.push(`/recipe/${recipe.id}`)
  }
}

// 组件挂载
onMounted(() => {
  fetchHomeStats()
  fetchLatestRecipes()
  fetchRecommendedRecipes()
  fetchPopularCreators()
  fetchHotKeywords()
})
</script>

<style scoped>
.home-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeef0 0%, #fff5f7 50%, #ffffff 100%);
}

/* 英雄区域 */
.hero-section {
  background: linear-gradient(135deg, #ff9aa2 0%, #ffb3ba 50%, #ffcccb 100%);
  padding: 80px 40px 60px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.3rem;
  margin-bottom: 3rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 搜索区域 */
.search-container {
  margin-bottom: 2rem;
}

.search-input {
  max-width: 600px;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
}

.search-input :deep(.el-input-group__append) {
  border-radius: 0 25px 25px 0;
  background: #ff6b7a;
  border: none;
  color: white;
}

/* 热门搜索词 */
.hot-keywords {
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
}

.hot-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-right: 8px;
}

.keyword-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.keyword-tag:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  padding: 15px 30px;
  font-size: 1rem;
  border-radius: 25px;
  min-width: 140px;
}

.action-buttons .el-button--primary {
  background: #ff6b7a;
  border-color: #ff6b7a;
  box-shadow: 0 4px 12px rgba(255, 107, 122, 0.3);
}

.action-buttons .el-button--primary:hover {
  background: #ff5a6b;
  border-color: #ff5a6b;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 122, 0.4);
}

/* 页面容器 */
.page-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 统计数据区域 */
.stats-section {
  padding: 60px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.stats-card {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 24px rgba(255, 154, 162, 0.1);
  border: 1px solid rgba(255, 154, 162, 0.1);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 32px rgba(255, 154, 162, 0.15);
}

.stats-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: 600;
  color: #ff6b7a;
  margin-bottom: 20px;
}

.stats-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #ff6b7a;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

/* 内容区域 */
.content-section {
  padding: 40px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
}

/* 食谱网格 */
.recipe-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
}

.recipe-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(255, 154, 162, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 154, 162, 0.1);
}

.recipe-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 32px rgba(255, 154, 162, 0.2);
}

.recipe-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.recipe-image .el-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.recipe-card:hover .recipe-image .el-image {
  transform: scale(1.05);
}

.recipe-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 15px;
}

.recipe-stats {
  display: flex;
  gap: 12px;
}

.recipe-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
  color: white;
  font-size: 0.85rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  backdrop-filter: blur(4px);
}

.recipe-difficulty {
  background: rgba(255, 107, 122, 0.9);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.recipe-content {
  padding: 20px;
}

.recipe-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recipe-desc {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.creator-name {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

.recipe-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.85rem;
  color: #999;
}

/* 创作者网格 */
.creators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
}

.creator-card {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 16px rgba(255, 154, 162, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 154, 162, 0.1);
}

.creator-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 32px rgba(255, 154, 162, 0.2);
}

.creator-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.creator-info {
  flex: 1;
}

.creator-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.creator-bio {
  font-size: 0.85rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.creator-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  padding: 15px 0;
  background: #fafafa;
  border-radius: 12px;
}

.creator-stats .stat-item {
  text-align: center;
}

.creator-stats .stat-number {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ff6b7a;
  margin-bottom: 2px;
}

.creator-stats .stat-label {
  font-size: 0.8rem;
  color: #999;
}

.latest-recipe {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
}

.latest-recipe-cover {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.latest-recipe-cover .el-image {
  width: 100%;
  height: 100%;
}

.latest-recipe-info {
  flex: 1;
  min-width: 0;
}

.latest-recipe-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.latest-recipe-time {
  font-size: 0.8rem;
  color: #999;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}





/* 欢迎区域 */
.welcome-section {
  text-align: center;
  padding: 80px 40px;
}

.welcome-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  color: var(--text-secondary);
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 2rem;
}

.action-buttons .el-button {
  padding: 15px 30px;
  font-size: 1rem;
}

/* 功能卡片 */
.features-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  padding: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  text-align: center;
  padding: 40px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.feature-card:hover {
  transform: translateY(-5px);
  border-color: var(--primary-color);
  box-shadow: 0 15px 30px rgba(255, 154, 162, 0.2);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 预览区域 */
.preview-section {
  padding: 60px 40px;
  max-width: 1400px;
  margin: 0 auto;
}

.section-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 40px;
}

.recipe-preview {
  margin-bottom: 40px;
}

.preview-card {
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.preview-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.preview-image .el-image {
  width: 100%;
  height: 100%;
}

.recipe-stats {
  position: absolute;
  bottom: 10px;
  left: 10px;
  display: flex;
  gap: 15px;
}

.recipe-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
  color: white;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.6);
  padding: 4px 8px;
  border-radius: 12px;
}

.preview-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.recipe-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recipe-desc {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 15px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.time, .difficulty {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.preview-more {
  text-align: center;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    padding: 60px 20px;
  }
  
  .welcome-title {
    font-size: 2.5rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .action-buttons .el-button {
    width: 250px;
  }
  
  .features-section {
    grid-template-columns: 1fr;
    padding: 20px;
  }
  
  .preview-section {
    padding: 40px 20px;
  }

  .hero-section {
    padding: 60px 20px 40px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .search-input {
    max-width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stats-card {
    padding: 20px;
  }

  .stats-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .recipe-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .creators-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .page-container {
    padding: 0 15px;
  }

  .content-section {
    padding: 30px 0;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hot-keywords {
    justify-content: flex-start;
  }

  .stats-content {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .creator-header {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .creator-stats {
    flex-direction: column;
    gap: 10px;
  }
}
</style>