<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>食谱管理系统 - 管理端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
        }

        #app {
            height: 100vh;
            overflow: hidden;
        }

        /* 加载动画 */
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f0f0f0;
            border-top: 4px solid #409eff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
  <script type="module" crossorigin src="/assets/index-BDkHONMN.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-B-__EyTB.css">
</head>

<body>
    <div id="app">
        <div class="loading-container">
            <div class="loading-spinner"></div>
        </div>
    </div>

</body>

</html>