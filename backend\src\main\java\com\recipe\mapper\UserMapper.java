package com.recipe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.recipe.dto.PopularCreatorDTO;
import com.recipe.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户数据访问层接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名或邮箱查询用户
     * 
     * @param usernameOrEmail 用户名或邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE (username = #{usernameOrEmail} OR email = #{usernameOrEmail}) AND deleted = 0")
    User findByUsernameOrEmail(@Param("usernameOrEmail") String usernameOrEmail);

    /**
     * 检查用户名是否已存在
     * 
     * @param username 用户名
     * @return 存在返回用户数量，不存在返回0
     */
    @Select("SELECT COUNT(*) FROM users WHERE username = #{username} AND deleted = 0")
    int countByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否已存在
     * 
     * @param email 邮箱
     * @return 存在返回用户数量，不存在返回0
     */
    @Select("SELECT COUNT(*) FROM users WHERE email = #{email} AND deleted = 0")
    int countByEmail(@Param("email") String email);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = 0")
    User findByEmail(@Param("email") String email);

    /**
     * 获取热门创作者
     * 
     * @param currentUserId 当前用户ID（用于判断关注状态，可为空）
     * @param limit         限制数量
     * @return 热门创作者列表
     */
    @Select("""
                SELECT u.id, u.username, u.nickname, u.avatar, u.bio,
                       COUNT(DISTINCT r.id) as recipe_count,
                       COALESCE(SUM(r.view_count), 0) as total_views,
                       COALESCE(SUM(r.like_count), 0) as total_likes,
                       COALESCE(SUM(r.favorite_count), 0) as total_favorites,
                       (SELECT COUNT(*) FROM follows WHERE following_id = u.id) as followers_count,
                       (SELECT COUNT(*) FROM follows WHERE follower_id = u.id) as following_count,
                       (SELECT r2.title FROM recipes r2 WHERE r2.user_id = u.id AND r2.status = 1
                        ORDER BY r2.created_at DESC LIMIT 1) as latest_recipe_title,
                       (SELECT r2.cover_image FROM recipes r2 WHERE r2.user_id = u.id AND r2.status = 1
                        ORDER BY r2.created_at DESC LIMIT 1) as latest_recipe_cover,
                       (SELECT r2.created_at FROM recipes r2 WHERE r2.user_id = u.id AND r2.status = 1
                        ORDER BY r2.created_at DESC LIMIT 1) as latest_publish_time,
                       CASE WHEN #{currentUserId} IS NOT NULL AND EXISTS(
                           SELECT 1 FROM follows WHERE follower_id = #{currentUserId} AND following_id = u.id
                       ) THEN true ELSE false END as is_followed
                FROM users u
                LEFT JOIN recipes r ON u.id = r.user_id AND r.status = 1 AND r.deleted = 0
                WHERE u.deleted = 0
                GROUP BY u.id, u.username, u.nickname, u.avatar, u.bio
                HAVING recipe_count > 0
                ORDER BY (total_views * 0.2 + total_likes * 0.3 + total_favorites * 0.3 + recipe_count * 0.2) DESC
                LIMIT #{limit}
            """)
    List<PopularCreatorDTO> selectPopularCreators(@Param("currentUserId") Long currentUserId,
            @Param("limit") Integer limit);
}