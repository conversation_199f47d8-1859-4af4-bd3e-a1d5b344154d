/**
 * 文件工具函数
 */

// 获取API基础URL
const getApiBaseUrl = () => {
    return import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api'
}

/**
 * 将相对文件路径转换为完整的访问URL
 * @param filePath 相对文件路径，如 '/files/avatars/xxx.jpg'
 * @returns 完整的文件访问URL
 */
export const getFileUrl = (filePath?: string): string => {
    if (!filePath) {
        return ''
    }

    // 如果已经是完整URL，直接返回
    if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
        return filePath
    }

    // 移除开头的斜杠，因为会与API基础URL拼接
    const cleanPath = filePath.startsWith('/') ? filePath.substring(1) : filePath

    // 拼接完整URL（文件访问需要通过 /api 路径）
    return `${getApiBaseUrl()}/${cleanPath}`
}

/**
 * 获取默认头像URL
 * @returns 默认头像的URL
 */
export const getDefaultAvatarUrl = (): string => {
    // 可以返回一个默认头像的URL或者base64图片
    return '/default-avatar.png'
}

/**
 * 验证图片文件类型
 * @param file 文件对象
 * @returns 是否为有效的图片文件
 */
export const isValidImageFile = (file: File): boolean => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    return validTypes.includes(file.type)
}

/**
 * 验证文件大小
 * @param file 文件对象
 * @param maxSizeMB 最大文件大小（MB）
 * @returns 是否符合大小要求
 */
export const isValidFileSize = (file: File, maxSizeMB: number = 2): boolean => {
    const maxSizeBytes = maxSizeMB * 1024 * 1024
    return file.size <= maxSizeBytes
}

/**
 * 格式化文件大小
 * @param bytes 文件大小（字节）
 * @returns 格式化的文件大小字符串
 */
export const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
} 