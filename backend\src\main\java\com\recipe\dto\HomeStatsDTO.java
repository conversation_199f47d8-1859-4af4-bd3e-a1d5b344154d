package com.recipe.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 首页统计数据DTO
 */
@Data
@Schema(description = "首页统计数据")
public class HomeStatsDTO {

    @Schema(description = "全站统计数据")
    private GlobalStats globalStats;

    @Schema(description = "用户个人统计数据（仅登录用户）")
    private UserStats userStats;

    @Data
    @Schema(description = "全站统计数据")
    public static class GlobalStats {
        @Schema(description = "总用户数")
        private Long totalUsers;

        @Schema(description = "总食谱数")
        private Long totalRecipes;

        @Schema(description = "总浏览量")
        private Long totalViews;

        @Schema(description = "总点赞数")
        private Long totalLikes;

        @Schema(description = "总收藏数")
        private Long totalFavorites;

        @Schema(description = "今日新增用户数")
        private Long todayNewUsers;

        @Schema(description = "今日新增食谱数")
        private Long todayNewRecipes;

        @Schema(description = "活跃用户数（近7天）")
        private Long activeUsers;
    }

    @Data
    @Schema(description = "用户个人统计数据")
    public static class UserStats {
        @Schema(description = "我的食谱数")
        private Long myRecipeCount;

        @Schema(description = "我的收藏数")
        private Long myFavoriteCount;

        @Schema(description = "我的关注数")
        private Long myFollowingCount;

        @Schema(description = "我的粉丝数")
        private Long myFollowersCount;

        @Schema(description = "我的食谱总浏览量")
        private Long myTotalViews;

        @Schema(description = "我的食谱总点赞数")
        private Long myTotalLikes;
    }
}
